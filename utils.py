import pathlib
import pandas as pd

def load_latest_snapshot(processed_dir: pathlib.Path) -> pd.DataFrame:
    files = list(processed_dir.glob('features_*.parquet'))
    latest = max(files, key=lambda p: p.stat().st_mtime)
    return pd.read_parquet(latest, engine='fastparquet')

def load_latest_timeseries(processed_dir: pathlib.Path) -> pd.DataFrame:
    files = list(processed_dir.glob('rs_timeseries_*.parquet'))
    latest = max(files, key=lambda p: p.stat().st_mtime)
    return pd.read_parquet(latest, engine='fastparquet')