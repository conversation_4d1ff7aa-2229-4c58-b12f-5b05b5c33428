# Enhanced Relative Strength System Guide

## Overview
The enhanced relative strength (RS) system provides multi-level RS scoring with time series tracking, enabling comprehensive analysis of stock performance across different contexts.

## Key Features

### 1. Multi-Level RS Scores
- **Global RS**: Ranks tickers against the entire universe
- **Theme RS**: Ranks tickers within their theme (e.g., AI, Fintech)
- **Subtheme RS**: Ranks tickers within their subtheme (e.g., Cloud Services, Payment Processors)

### 2. Time Series Tracking
- Complete historical RS scores for each ticker
- Rolling statistics and trend analysis
- Theme and subtheme performance over time

### 3. Percentile Rankings
- Easy-to-interpret percentile scores (0-100%)
- Within-group rankings for themes and subthemes
- Divergence detection between different RS levels

## System Components

### Core Classes

#### `RelativeStrengthCalculator`
Main calculation engine for enhanced RS scores.

```python
from relative_strength_calculator import RelativeStrengthCalculator

rs_calc = RelativeStrengthCalculator(
    rs_horizons=['return_20d', 'return_22d', 'return_200d'],
    weights={'return_20d': 0.4, 'return_22d': 0.3, 'return_200d': 0.3}
)

# Compute all RS scores
rs_scores = rs_calc.compute_all_rs_scores(returns, metadata)
```

#### `RSAnalyzer`
Comprehensive analysis toolkit for RS data.

```python
from rs_analysis_toolkit import RSAnalyzer

analyzer = RSAnalyzer()  # Uses latest data
report = analyzer.generate_rs_report()
```

### Enhanced FeatureEngineer
Updated to include multi-level RS calculation when metadata is provided.

```python
fe = FeatureEngineer()
result = fe.compute(close_df, metadata)  # Pass metadata for enhanced RS
```

## Usage Examples

### 1. Basic RS Calculation
```python
# Run the complete pipeline with enhanced RS
python main.py

# Or programmatically
from main import main
dashboard, output_path = main("default_tickers.csv")
```

### 2. RS Analysis Demo
```python
# Comprehensive RS demonstration
python rs_example.py

# Analysis toolkit demo
python rs_analysis_toolkit.py
```

### 3. Custom Analysis
```python
from rs_analysis_toolkit import RSAnalyzer

# Initialize analyzer
analyzer = RSAnalyzer()

# Top performers by different RS types
global_top = analyzer.get_top_performers('rs_global', 10)
theme_top = analyzer.get_top_performers('rs_theme', 10)

# Theme analysis
theme_stats = analyzer.analyze_by_theme()

# Find divergences
divergent_stocks = analyzer.find_rs_divergences(threshold=0.3)

# Track individual ticker
ticker_history = analyzer.track_ticker_timeseries('AAPL', 'global')
```

## Output Files

### Enhanced RS Time Series
- `rs_global_timeseries_YYYYMMDD_HHMMSS.parquet`: Global RS scores over time
- `rs_theme_timeseries_YYYYMMDD_HHMMSS.parquet`: Theme-relative RS scores
- `rs_subtheme_timeseries_YYYYMMDD_HHMMSS.parquet`: Subtheme-relative RS scores

### Enhanced Snapshot
The feature snapshot now includes:
- `rs_global`: Global RS score (0-1)
- `rs_theme`: Theme-relative RS score (0-1)
- `rs_subtheme`: Subtheme-relative RS score (0-1)
- `rs_global_pct`: Global percentile rank (0-100%)
- `rs_theme_pct`: Theme percentile rank (0-100%)
- `rs_subtheme_pct`: Subtheme percentile rank (0-100%)
- `Theme`: Stock's theme classification
- `Subtheme`: Stock's subtheme classification

## Interpretation Guide

### RS Score Ranges
- **0.8-1.0**: Top quintile performers
- **0.6-0.8**: Above average performers
- **0.4-0.6**: Average performers
- **0.2-0.4**: Below average performers
- **0.0-0.2**: Bottom quintile performers

### Divergence Analysis
Look for stocks with:
- **High Theme RS + Low Global RS**: Strong within weak theme
- **Low Theme RS + High Global RS**: Weak within strong theme
- **High Subtheme RS + Low Theme RS**: Niche leader in struggling sector

### Time Series Patterns
- **Rising RS Trend**: Improving relative performance
- **Falling RS Trend**: Deteriorating relative performance
- **High RS Volatility**: Inconsistent performance
- **RS Momentum**: Acceleration in RS trend

## Advanced Analysis Examples

### 1. Theme Rotation Analysis
```python
# Compare theme performance over time
themes = ['AI', 'Fintech', 'Cybersecurity']
theme_comparison = analyzer.compare_themes_over_time(themes)

# Identify rotating themes
theme_momentum = theme_comparison.diff(5).iloc[-1].sort_values(ascending=False)
```

### 2. Sector Leadership Analysis
```python
# Find theme leaders
for theme in analyzer.snapshot['Theme'].unique():
    theme_leaders = analyzer.get_top_performers('rs_theme', 3)
    theme_data = theme_leaders[theme_leaders['Theme'] == theme]
    print(f"{theme} leaders: {list(theme_data.index)}")
```

### 3. RS Momentum Screening
```python
# Find stocks with improving RS trends
for ticker in analyzer.snapshot.index[:10]:  # Sample
    ticker_ts = analyzer.track_ticker_timeseries(ticker)
    if not ticker_ts.empty and len(ticker_ts) > 20:
        recent_trend = ticker_ts['rs_score'].tail(10).diff().mean()
        if recent_trend > 0.01:  # Improving RS
            print(f"{ticker}: RS improving (+{recent_trend:.4f})")
```

## Best Practices

### 1. Multi-Level Analysis
Always consider RS scores at all levels:
- Use global RS for overall market positioning
- Use theme RS for sector rotation strategies
- Use subtheme RS for niche opportunities

### 2. Divergence Opportunities
Pay special attention to divergences:
- High theme RS + low global RS = potential theme rotation play
- High subtheme RS + low theme RS = potential niche leader

### 3. Time Series Context
- Look at RS trends, not just current levels
- Consider RS volatility for risk assessment
- Use moving averages to smooth noise

### 4. Combine with Fundamentals
RS scores are technical indicators - combine with:
- Fundamental analysis
- Earnings trends
- Valuation metrics
- Market conditions

## Configuration Options

### RS Horizons
Default: `['return_20d', 'return_22d', 'return_200d']`
- Short-term: 1d, 5d, 10d
- Medium-term: 20d, 22d, 60d
- Long-term: 120d, 200d, 252d

### Weights
Default: Equal weights
- Momentum focus: Higher weight on shorter horizons
- Trend focus: Higher weight on longer horizons
- Custom: Based on your strategy requirements

## Troubleshooting

### Common Issues
1. **Missing enhanced RS scores**: Ensure metadata is passed to FeatureEngineer
2. **Empty theme analysis**: Check Theme/Subtheme columns in metadata
3. **No timeseries data**: Run pipeline with enhanced RS enabled first

### Performance Tips
1. Use smaller ticker universes for faster computation
2. Adjust lookback period based on analysis needs
3. Cache results for repeated analysis

## Integration with Existing Workflow

The enhanced RS system is fully backward compatible:
- Legacy `rs_score` column still available
- Existing code continues to work
- Enhanced features available when metadata provided
- Gradual migration path available
