2025-06-28 07:42:16,139 - downloader - INFO - Starting to fetch close prices for 7 tickers
2025-06-28 07:42:16,174 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-28 07:42:16,175 - downloader - INFO - Using cached OHLC data for AAPL from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\AAPL.parquet
2025-06-28 07:46:48,945 - downloader - INFO - Starting to fetch close prices for 7 tickers
2025-06-28 07:46:48,953 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-28 07:46:48,954 - downloader - INFO - Using cached OHLC data for AAPL from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\AAPL.parquet
2025-06-28 07:46:49,184 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-28 07:46:49,184 - downloader - INFO - Using cached OHLC data for MSFT from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\MSFT.parquet
2025-06-28 07:46:49,189 - downloader - INFO - Fetching OHLC data for GOOGL
2025-06-28 07:46:49,191 - downloader - INFO - Using cached OHLC data for GOOGL from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\GOOGL.parquet
2025-06-28 07:46:49,195 - downloader - INFO - Fetching OHLC data for NVDA
2025-06-28 07:46:49,195 - downloader - INFO - Using cached OHLC data for NVDA from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\NVDA.parquet
2025-06-28 07:46:49,198 - downloader - INFO - Fetching OHLC data for TSLA
2025-06-28 07:46:49,198 - downloader - INFO - Using cached OHLC data for TSLA from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\TSLA.parquet
2025-06-28 07:46:49,202 - downloader - INFO - Fetching OHLC data for META
2025-06-28 07:46:49,202 - downloader - INFO - Downloading OHLC data for META from API
2025-06-28 07:46:49,618 - downloader - INFO - Successfully downloaded 60 records for META
2025-06-28 07:46:49,630 - downloader - INFO - Cached OHLC data for META to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\META.parquet
2025-06-28 07:46:49,633 - downloader - INFO - Fetching OHLC data for AMZN
2025-06-28 07:46:49,633 - downloader - INFO - Downloading OHLC data for AMZN from API
2025-06-28 07:46:49,991 - downloader - INFO - Successfully downloaded 60 records for AMZN
2025-06-28 07:46:50,001 - downloader - INFO - Cached OHLC data for AMZN to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\AMZN.parquet
2025-06-28 07:46:50,004 - downloader - INFO - OHLC download completed: 7 successful, 0 failed
2025-06-28 07:46:50,008 - downloader - INFO - Combined close DataFrame shape: (60, 7)
2025-06-28 07:46:50,008 - downloader - INFO - Starting to fetch company profiles for 7 tickers
2025-06-28 07:46:50,010 - downloader - INFO - Using cached profile for AAPL from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\AAPL.parquet
2025-06-28 07:46:50,013 - downloader - INFO - Using cached profile for MSFT from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\MSFT.parquet
2025-06-28 07:46:50,017 - downloader - INFO - Using cached profile for GOOGL from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\GOOGL.parquet
2025-06-28 07:46:50,020 - downloader - INFO - Using cached profile for NVDA from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\NVDA.parquet
2025-06-28 07:46:50,024 - downloader - INFO - Using cached profile for TSLA from recent directory S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\TSLA.parquet
2025-06-28 07:46:50,026 - downloader - INFO - Downloading profile for META from API
2025-06-28 07:46:50,407 - downloader - INFO - Cached profile for META to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\META.parquet
2025-06-28 07:46:50,408 - downloader - INFO - Downloading profile for AMZN from API
2025-06-28 07:46:50,776 - downloader - INFO - Cached profile for AMZN to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\AMZN.parquet
2025-06-28 07:46:50,776 - downloader - INFO - Profile download completed: 2 downloaded, 5 cached, 0 failed
2025-06-28 07:46:50,778 - downloader - INFO - Combined profile DataFrame shape: (7, 4)
2025-06-28 07:49:02,029 - downloader - INFO - Starting to fetch close prices for 7 tickers
2025-06-28 07:49:02,035 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-28 07:49:02,036 - downloader - INFO - Downloading OHLC data for AAPL from API
2025-06-28 07:49:02,427 - downloader - INFO - Successfully downloaded 60 records for AAPL
2025-06-28 07:49:02,576 - downloader - INFO - Cached OHLC data for AAPL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\AAPL.parquet
2025-06-28 07:49:02,578 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-28 07:49:02,579 - downloader - INFO - Downloading OHLC data for MSFT from API
2025-06-28 07:49:02,934 - downloader - INFO - Successfully downloaded 60 records for MSFT
2025-06-28 07:49:02,946 - downloader - INFO - Cached OHLC data for MSFT to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\MSFT.parquet
2025-06-28 07:49:02,947 - downloader - INFO - Fetching OHLC data for GOOGL
2025-06-28 07:49:02,948 - downloader - INFO - Downloading OHLC data for GOOGL from API
2025-06-28 07:49:03,307 - downloader - INFO - Successfully downloaded 60 records for GOOGL
2025-06-28 07:49:03,325 - downloader - INFO - Cached OHLC data for GOOGL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\GOOGL.parquet
2025-06-28 07:49:03,327 - downloader - INFO - Fetching OHLC data for NVDA
2025-06-28 07:49:03,327 - downloader - INFO - Downloading OHLC data for NVDA from API
2025-06-28 07:49:03,681 - downloader - INFO - Successfully downloaded 60 records for NVDA
2025-06-28 07:49:03,693 - downloader - INFO - Cached OHLC data for NVDA to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\NVDA.parquet
2025-06-28 07:49:03,694 - downloader - INFO - Fetching OHLC data for TSLA
2025-06-28 07:49:03,695 - downloader - INFO - Downloading OHLC data for TSLA from API
2025-06-28 07:49:04,051 - downloader - INFO - Successfully downloaded 60 records for TSLA
2025-06-28 07:49:04,063 - downloader - INFO - Cached OHLC data for TSLA to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\TSLA.parquet
2025-06-28 07:49:04,065 - downloader - INFO - Fetching OHLC data for META
2025-06-28 07:49:04,065 - downloader - INFO - Using cached OHLC data for META from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\META.parquet
2025-06-28 07:49:04,076 - downloader - INFO - Fetching OHLC data for AMZN
2025-06-28 07:49:04,077 - downloader - INFO - Using cached OHLC data for AMZN from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\AMZN.parquet
2025-06-28 07:49:04,080 - downloader - INFO - OHLC download completed: 7 successful, 0 failed
2025-06-28 07:49:04,082 - downloader - INFO - Combined close DataFrame shape: (60, 7)
2025-06-28 07:49:04,082 - downloader - INFO - Starting to fetch company profiles for 7 tickers
2025-06-28 07:49:04,083 - downloader - INFO - Downloading profile for AAPL from API
2025-06-28 07:49:04,458 - downloader - INFO - Cached profile for AAPL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\AAPL.parquet
2025-06-28 07:49:04,459 - downloader - INFO - Downloading profile for MSFT from API
2025-06-28 07:49:04,823 - downloader - INFO - Cached profile for MSFT to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\MSFT.parquet
2025-06-28 07:49:04,824 - downloader - INFO - Downloading profile for GOOGL from API
2025-06-28 07:49:05,185 - downloader - INFO - Cached profile for GOOGL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\GOOGL.parquet
2025-06-28 07:49:05,186 - downloader - INFO - Downloading profile for NVDA from API
2025-06-28 07:49:05,563 - downloader - INFO - Cached profile for NVDA to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\NVDA.parquet
2025-06-28 07:49:05,564 - downloader - INFO - Downloading profile for TSLA from API
2025-06-28 07:49:05,924 - downloader - INFO - Cached profile for TSLA to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\TSLA.parquet
2025-06-28 07:49:05,924 - downloader - INFO - Using cached profile for META from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\META.parquet
2025-06-28 07:49:05,927 - downloader - INFO - Using cached profile for AMZN from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\AMZN.parquet
2025-06-28 07:49:05,930 - downloader - INFO - Profile download completed: 5 downloaded, 2 cached, 0 failed
2025-06-28 07:49:05,931 - downloader - INFO - Combined profile DataFrame shape: (7, 4)
2025-06-28 08:05:41,517 - downloader - INFO - Starting to fetch close prices for 3 tickers
2025-06-28 08:05:41,524 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-28 08:05:41,524 - downloader - INFO - Using cached OHLC data for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\AAPL.parquet
2025-06-28 08:05:41,685 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-28 08:05:41,685 - downloader - INFO - Using cached OHLC data for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\MSFT.parquet
2025-06-28 08:05:41,688 - downloader - INFO - Fetching OHLC data for GOOGL
2025-06-28 08:05:41,689 - downloader - INFO - Using cached OHLC data for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\GOOGL.parquet
2025-06-28 08:05:41,693 - downloader - INFO - OHLC download completed: 3 successful, 0 failed
2025-06-28 08:05:41,694 - downloader - INFO - Combined close DataFrame shape: (60, 3)
2025-06-28 08:05:41,695 - downloader - INFO - Starting to fetch company profiles for 3 tickers
2025-06-28 08:05:41,696 - downloader - INFO - Using cached profile for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\AAPL.parquet
2025-06-28 08:05:41,699 - downloader - INFO - Using cached profile for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\MSFT.parquet
2025-06-28 08:05:41,701 - downloader - INFO - Using cached profile for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\GOOGL.parquet
2025-06-28 08:05:41,704 - downloader - INFO - Profile download completed: 0 downloaded, 3 cached, 0 failed
2025-06-28 08:05:41,705 - downloader - INFO - Combined profile DataFrame shape: (3, 4)
2025-06-28 08:22:18,839 - downloader - INFO - Starting to fetch close prices for 5 tickers
2025-06-28 08:22:18,849 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-28 08:22:18,849 - downloader - INFO - Using cached OHLC data for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\AAPL.parquet
2025-06-28 08:22:19,003 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-28 08:22:19,004 - downloader - INFO - Using cached OHLC data for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\MSFT.parquet
2025-06-28 08:22:19,007 - downloader - INFO - Fetching OHLC data for GOOGL
2025-06-28 08:22:19,007 - downloader - INFO - Using cached OHLC data for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\GOOGL.parquet
2025-06-28 08:22:19,010 - downloader - INFO - Fetching OHLC data for NVDA
2025-06-28 08:22:19,010 - downloader - INFO - Using cached OHLC data for NVDA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\NVDA.parquet
2025-06-28 08:22:19,014 - downloader - INFO - Fetching OHLC data for TSLA
2025-06-28 08:22:19,015 - downloader - INFO - Using cached OHLC data for TSLA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\TSLA.parquet
2025-06-28 08:22:19,020 - downloader - INFO - OHLC download completed: 5 successful, 0 failed
2025-06-28 08:22:19,022 - downloader - INFO - Combined close DataFrame shape: (60, 5)
2025-06-28 08:22:19,022 - downloader - INFO - Starting to fetch company profiles for 5 tickers
2025-06-28 08:22:19,023 - downloader - INFO - Using cached profile for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\AAPL.parquet
2025-06-28 08:22:19,026 - downloader - INFO - Using cached profile for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\MSFT.parquet
2025-06-28 08:22:19,028 - downloader - INFO - Using cached profile for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\GOOGL.parquet
2025-06-28 08:22:19,031 - downloader - INFO - Using cached profile for NVDA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\NVDA.parquet
2025-06-28 08:22:19,033 - downloader - INFO - Using cached profile for TSLA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\TSLA.parquet
2025-06-28 08:22:19,037 - downloader - INFO - Profile download completed: 0 downloaded, 5 cached, 0 failed
2025-06-28 08:22:19,039 - downloader - INFO - Combined profile DataFrame shape: (5, 4)
2025-06-28 08:22:19,318 - downloader - INFO - Starting to fetch close prices for 5 tickers
2025-06-28 08:22:19,318 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-28 08:22:19,319 - downloader - INFO - Using cached OHLC data for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\AAPL.parquet
2025-06-28 08:22:19,323 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-28 08:22:19,324 - downloader - INFO - Using cached OHLC data for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\MSFT.parquet
2025-06-28 08:22:19,330 - downloader - INFO - Fetching OHLC data for GOOGL
2025-06-28 08:22:19,330 - downloader - INFO - Using cached OHLC data for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\GOOGL.parquet
2025-06-28 08:22:19,334 - downloader - INFO - Fetching OHLC data for NVDA
2025-06-28 08:22:19,334 - downloader - INFO - Using cached OHLC data for NVDA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\NVDA.parquet
2025-06-28 08:22:19,339 - downloader - INFO - Fetching OHLC data for TSLA
2025-06-28 08:22:19,339 - downloader - INFO - Using cached OHLC data for TSLA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\TSLA.parquet
2025-06-28 08:22:19,343 - downloader - INFO - OHLC download completed: 5 successful, 0 failed
2025-06-28 08:22:19,344 - downloader - INFO - Combined close DataFrame shape: (60, 5)
2025-06-28 08:22:19,344 - downloader - INFO - Starting to fetch company profiles for 5 tickers
2025-06-28 08:22:19,346 - downloader - INFO - Using cached profile for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\AAPL.parquet
2025-06-28 08:22:19,350 - downloader - INFO - Using cached profile for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\MSFT.parquet
2025-06-28 08:22:19,353 - downloader - INFO - Using cached profile for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\GOOGL.parquet
2025-06-28 08:22:19,356 - downloader - INFO - Using cached profile for NVDA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\NVDA.parquet
2025-06-28 08:22:19,359 - downloader - INFO - Using cached profile for TSLA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\TSLA.parquet
2025-06-28 08:22:19,361 - downloader - INFO - Profile download completed: 0 downloaded, 5 cached, 0 failed
2025-06-28 08:22:19,363 - downloader - INFO - Combined profile DataFrame shape: (5, 4)
2025-06-28 08:22:19,676 - downloader - INFO - Starting to fetch close prices for 5 tickers
2025-06-28 08:22:19,677 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-28 08:22:19,678 - downloader - INFO - Using cached OHLC data for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\AAPL.parquet
2025-06-28 08:22:19,683 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-28 08:22:19,684 - downloader - INFO - Using cached OHLC data for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\MSFT.parquet
2025-06-28 08:22:19,688 - downloader - INFO - Fetching OHLC data for GOOGL
2025-06-28 08:22:19,689 - downloader - INFO - Using cached OHLC data for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\GOOGL.parquet
2025-06-28 08:22:19,695 - downloader - INFO - Fetching OHLC data for NVDA
2025-06-28 08:22:19,695 - downloader - INFO - Using cached OHLC data for NVDA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\NVDA.parquet
2025-06-28 08:22:19,701 - downloader - INFO - Fetching OHLC data for TSLA
2025-06-28 08:22:19,701 - downloader - INFO - Using cached OHLC data for TSLA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\TSLA.parquet
2025-06-28 08:22:19,705 - downloader - INFO - OHLC download completed: 5 successful, 0 failed
2025-06-28 08:22:19,706 - downloader - INFO - Combined close DataFrame shape: (60, 5)
2025-06-28 08:22:19,706 - downloader - INFO - Starting to fetch company profiles for 5 tickers
2025-06-28 08:22:19,707 - downloader - INFO - Using cached profile for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\AAPL.parquet
2025-06-28 08:22:19,712 - downloader - INFO - Using cached profile for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\MSFT.parquet
2025-06-28 08:22:19,716 - downloader - INFO - Using cached profile for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\GOOGL.parquet
2025-06-28 08:22:19,719 - downloader - INFO - Using cached profile for NVDA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\NVDA.parquet
2025-06-28 08:22:19,722 - downloader - INFO - Using cached profile for TSLA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\TSLA.parquet
2025-06-28 08:22:19,724 - downloader - INFO - Profile download completed: 0 downloaded, 5 cached, 0 failed
2025-06-28 08:22:19,726 - downloader - INFO - Combined profile DataFrame shape: (5, 4)
2025-06-28 08:22:19,797 - downloader - INFO - Starting to fetch close prices for 5 tickers
2025-06-28 08:22:19,799 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-28 08:22:19,799 - downloader - INFO - Using cached OHLC data for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\AAPL.parquet
2025-06-28 08:22:19,803 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-28 08:22:19,803 - downloader - INFO - Using cached OHLC data for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\MSFT.parquet
2025-06-28 08:22:19,807 - downloader - INFO - Fetching OHLC data for GOOGL
2025-06-28 08:22:19,808 - downloader - INFO - Using cached OHLC data for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\GOOGL.parquet
2025-06-28 08:22:19,813 - downloader - INFO - Fetching OHLC data for NVDA
2025-06-28 08:22:19,814 - downloader - INFO - Using cached OHLC data for NVDA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\NVDA.parquet
2025-06-28 08:22:19,819 - downloader - INFO - Fetching OHLC data for TSLA
2025-06-28 08:22:19,820 - downloader - INFO - Using cached OHLC data for TSLA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_28\TSLA.parquet
2025-06-28 08:22:19,823 - downloader - INFO - OHLC download completed: 5 successful, 0 failed
2025-06-28 08:22:19,825 - downloader - INFO - Combined close DataFrame shape: (60, 5)
2025-06-28 08:22:19,825 - downloader - INFO - Starting to fetch company profiles for 5 tickers
2025-06-28 08:22:19,826 - downloader - INFO - Using cached profile for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\AAPL.parquet
2025-06-28 08:22:19,829 - downloader - INFO - Using cached profile for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\MSFT.parquet
2025-06-28 08:22:19,833 - downloader - INFO - Using cached profile for GOOGL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\GOOGL.parquet
2025-06-28 08:22:19,836 - downloader - INFO - Using cached profile for NVDA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\NVDA.parquet
2025-06-28 08:22:19,839 - downloader - INFO - Using cached profile for TSLA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_28\TSLA.parquet
2025-06-28 08:22:19,843 - downloader - INFO - Profile download completed: 0 downloaded, 5 cached, 0 failed
2025-06-28 08:22:19,845 - downloader - INFO - Combined profile DataFrame shape: (5, 4)
