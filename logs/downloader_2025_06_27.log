2025-06-27 21:26:50,364 - downloader - INFO - Starting to fetch close prices for 5 tickers
2025-06-27 21:26:50,376 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-27 21:26:50,378 - downloader - INFO - Downloading OHLC data for AAPL from API
2025-06-27 21:26:50,754 - downloader - INFO - Successfully downloaded 30 records for AAPL
2025-06-27 21:26:50,852 - downloader - INFO - Cached OHLC data for AAPL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\AAPL.parquet
2025-06-27 21:26:50,853 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-27 21:26:50,853 - downloader - INFO - Downloading OHLC data for MSFT from API
2025-06-27 21:26:51,199 - downloader - INFO - Successfully downloaded 30 records for MSFT
2025-06-27 21:26:51,220 - downloader - INFO - Cached OHLC data for MSFT to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\MSFT.parquet
2025-06-27 21:26:51,221 - downloader - INFO - Fetching OHLC data for GOOGL
2025-06-27 21:26:51,221 - downloader - INFO - Downloading OHLC data for GOOGL from API
2025-06-27 21:26:51,575 - downloader - INFO - Successfully downloaded 30 records for GOOGL
2025-06-27 21:26:51,585 - downloader - INFO - Cached OHLC data for GOOGL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\GOOGL.parquet
2025-06-27 21:26:51,585 - downloader - INFO - Fetching OHLC data for NVDA
2025-06-27 21:26:51,586 - downloader - INFO - Downloading OHLC data for NVDA from API
2025-06-27 21:26:51,930 - downloader - INFO - Successfully downloaded 30 records for NVDA
2025-06-27 21:26:51,941 - downloader - INFO - Cached OHLC data for NVDA to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\NVDA.parquet
2025-06-27 21:26:51,941 - downloader - INFO - Fetching OHLC data for TSLA
2025-06-27 21:26:51,942 - downloader - INFO - Downloading OHLC data for TSLA from API
2025-06-27 21:26:52,321 - downloader - INFO - Successfully downloaded 30 records for TSLA
2025-06-27 21:26:52,333 - downloader - INFO - Cached OHLC data for TSLA to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\TSLA.parquet
2025-06-27 21:26:52,333 - downloader - INFO - OHLC download completed: 5 successful, 0 failed
2025-06-27 21:26:52,334 - downloader - INFO - Combined close DataFrame shape: (30, 5)
2025-06-27 21:26:52,334 - downloader - INFO - Starting to fetch company profiles for 5 tickers
2025-06-27 21:26:52,335 - downloader - INFO - Downloading profile for AAPL from API
2025-06-27 21:26:52,726 - downloader - INFO - Cached profile for AAPL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\AAPL.parquet
2025-06-27 21:26:52,727 - downloader - INFO - Downloading profile for MSFT from API
2025-06-27 21:26:53,167 - downloader - INFO - Cached profile for MSFT to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\MSFT.parquet
2025-06-27 21:26:53,167 - downloader - INFO - Downloading profile for GOOGL from API
2025-06-27 21:26:53,658 - downloader - INFO - Cached profile for GOOGL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\GOOGL.parquet
2025-06-27 21:26:53,658 - downloader - INFO - Downloading profile for NVDA from API
2025-06-27 21:26:54,043 - downloader - INFO - Cached profile for NVDA to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\NVDA.parquet
2025-06-27 21:26:54,044 - downloader - INFO - Downloading profile for TSLA from API
2025-06-27 21:26:54,762 - downloader - INFO - Cached profile for TSLA to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\TSLA.parquet
2025-06-27 21:26:54,762 - downloader - INFO - Profile download completed: 5 downloaded, 0 cached, 0 failed
2025-06-27 21:26:54,763 - downloader - INFO - Combined profile DataFrame shape: (5, 4)
2025-06-27 21:26:54,765 - downloader - INFO - Starting to fetch close prices for 2 tickers
2025-06-27 21:26:54,765 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-27 21:26:54,765 - downloader - INFO - Using cached OHLC data for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\AAPL.parquet
2025-06-27 21:26:54,779 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-27 21:26:54,779 - downloader - INFO - Using cached OHLC data for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\MSFT.parquet
2025-06-27 21:26:54,782 - downloader - INFO - OHLC download completed: 2 successful, 0 failed
2025-06-27 21:26:54,782 - downloader - INFO - Combined close DataFrame shape: (30, 2)
2025-06-27 21:26:54,782 - downloader - INFO - Starting to fetch company profiles for 2 tickers
2025-06-27 21:26:54,782 - downloader - INFO - Using cached profile for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\AAPL.parquet
2025-06-27 21:26:54,786 - downloader - INFO - Using cached profile for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\MSFT.parquet
2025-06-27 21:26:54,788 - downloader - INFO - Profile download completed: 0 downloaded, 2 cached, 0 failed
2025-06-27 21:26:54,788 - downloader - INFO - Combined profile DataFrame shape: (2, 4)
2025-06-27 21:26:54,788 - downloader - INFO - Starting to fetch close prices for 2 tickers
2025-06-27 21:26:54,789 - downloader - INFO - Fetching OHLC data for AAPL
2025-06-27 21:26:54,789 - downloader - INFO - Using cached OHLC data for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\AAPL.parquet
2025-06-27 21:26:54,792 - downloader - INFO - Fetching OHLC data for MSFT
2025-06-27 21:26:54,792 - downloader - INFO - Using cached OHLC data for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\MSFT.parquet
2025-06-27 21:26:54,796 - downloader - INFO - OHLC download completed: 2 successful, 0 failed
2025-06-27 21:26:54,796 - downloader - INFO - Combined close DataFrame shape: (30, 2)
2025-06-27 21:26:54,796 - downloader - INFO - Starting to fetch company profiles for 2 tickers
2025-06-27 21:26:54,797 - downloader - INFO - Using cached profile for AAPL from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\AAPL.parquet
2025-06-27 21:26:54,799 - downloader - INFO - Using cached profile for MSFT from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\profiles\2025_06_27\MSFT.parquet
2025-06-27 21:26:54,802 - downloader - INFO - Profile download completed: 0 downloaded, 2 cached, 0 failed
2025-06-27 21:26:54,802 - downloader - INFO - Combined profile DataFrame shape: (2, 4)
