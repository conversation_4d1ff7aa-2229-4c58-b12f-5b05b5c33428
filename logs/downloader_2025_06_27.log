2025-06-27 21:36:21,084 - downloader - INFO - Starting to fetch close prices for 20 tickers
2025-06-27 21:36:21,091 - downloader - INFO - Fetching OHLC data for INFN
2025-06-27 21:36:21,091 - downloader - INFO - Using cached OHLC data for INFN from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\INFN.parquet
2025-06-27 21:36:21,182 - downloader - INFO - Fetching OHLC data for ADBE
2025-06-27 21:36:21,182 - downloader - INFO - Using cached OHLC data for ADBE from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\ADBE.parquet
2025-06-27 21:36:21,185 - downloader - INFO - Fetching OHLC data for NVTS
2025-06-27 21:36:21,185 - downloader - INFO - Using cached OHLC data for NVTS from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\NVTS.parquet
2025-06-27 21:36:21,188 - downloader - INFO - Fetching OHLC data for NBIS
2025-06-27 21:36:21,189 - downloader - INFO - Using cached OHLC data for NBIS from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\NBIS.parquet
2025-06-27 21:36:21,192 - downloader - INFO - Fetching OHLC data for CRWV
2025-06-27 21:36:21,192 - downloader - INFO - Using cached OHLC data for CRWV from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\CRWV.parquet
2025-06-27 21:36:21,195 - downloader - INFO - Fetching OHLC data for FSLY
2025-06-27 21:36:21,195 - downloader - INFO - Using cached OHLC data for FSLY from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\FSLY.parquet
2025-06-27 21:36:21,199 - downloader - INFO - Fetching OHLC data for PTON
2025-06-27 21:36:21,199 - downloader - INFO - Using cached OHLC data for PTON from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\PTON.parquet
2025-06-27 21:36:21,202 - downloader - INFO - Fetching OHLC data for ARM
2025-06-27 21:36:21,202 - downloader - INFO - Using cached OHLC data for ARM from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\ARM.parquet
2025-06-27 21:36:21,205 - downloader - INFO - Fetching OHLC data for AMD
2025-06-27 21:36:21,205 - downloader - INFO - Using cached OHLC data for AMD from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\AMD.parquet
2025-06-27 21:36:21,208 - downloader - INFO - Fetching OHLC data for INTC
2025-06-27 21:36:21,208 - downloader - INFO - Using cached OHLC data for INTC from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\INTC.parquet
2025-06-27 21:36:21,211 - downloader - INFO - Fetching OHLC data for VNET
2025-06-27 21:36:21,211 - downloader - INFO - Using cached OHLC data for VNET from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\VNET.parquet
2025-06-27 21:36:21,214 - downloader - INFO - Fetching OHLC data for STX
2025-06-27 21:36:21,214 - downloader - INFO - Using cached OHLC data for STX from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\STX.parquet
2025-06-27 21:36:21,217 - downloader - INFO - Fetching OHLC data for GDS
2025-06-27 21:36:21,218 - downloader - INFO - Downloading OHLC data for GDS from API
2025-06-27 21:36:21,608 - downloader - INFO - Successfully downloaded 90 records for GDS
2025-06-27 21:36:21,620 - downloader - INFO - Cached OHLC data for GDS to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\GDS.parquet
2025-06-27 21:36:21,620 - downloader - INFO - Fetching OHLC data for SMCI
2025-06-27 21:36:21,621 - downloader - INFO - Downloading OHLC data for SMCI from API
2025-06-27 21:36:21,970 - downloader - INFO - Successfully downloaded 90 records for SMCI
2025-06-27 21:36:21,981 - downloader - INFO - Cached OHLC data for SMCI to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\SMCI.parquet
2025-06-27 21:36:21,981 - downloader - INFO - Fetching OHLC data for MRVL
2025-06-27 21:36:21,982 - downloader - INFO - Downloading OHLC data for MRVL from API
2025-06-27 21:36:22,373 - downloader - INFO - Successfully downloaded 90 records for MRVL
2025-06-27 21:36:22,384 - downloader - INFO - Cached OHLC data for MRVL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\MRVL.parquet
2025-06-27 21:36:22,385 - downloader - INFO - Fetching OHLC data for IREN
2025-06-27 21:36:22,385 - downloader - INFO - Downloading OHLC data for IREN from API
2025-06-27 21:36:22,751 - downloader - INFO - Successfully downloaded 90 records for IREN
2025-06-27 21:36:22,762 - downloader - INFO - Cached OHLC data for IREN to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\IREN.parquet
2025-06-27 21:36:22,762 - downloader - INFO - Fetching OHLC data for ORCL
2025-06-27 21:36:22,763 - downloader - INFO - Downloading OHLC data for ORCL from API
2025-06-27 21:36:23,101 - downloader - INFO - Successfully downloaded 90 records for ORCL
2025-06-27 21:36:23,111 - downloader - INFO - Cached OHLC data for ORCL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\ORCL.parquet
2025-06-27 21:36:23,112 - downloader - INFO - Fetching OHLC data for HPE
2025-06-27 21:36:23,112 - downloader - INFO - Downloading OHLC data for HPE from API
2025-06-27 21:36:23,492 - downloader - INFO - Successfully downloaded 90 records for HPE
2025-06-27 21:36:23,503 - downloader - INFO - Cached OHLC data for HPE to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\HPE.parquet
2025-06-27 21:36:23,504 - downloader - INFO - Fetching OHLC data for NVDA
2025-06-27 21:36:23,504 - downloader - INFO - Using cached OHLC data for NVDA from S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\NVDA.parquet
2025-06-27 21:36:23,507 - downloader - INFO - Fetching OHLC data for DELL
2025-06-27 21:36:23,507 - downloader - INFO - Downloading OHLC data for DELL from API
2025-06-27 21:36:23,851 - downloader - INFO - Successfully downloaded 90 records for DELL
2025-06-27 21:36:23,866 - downloader - INFO - Cached OHLC data for DELL to S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\raw\2025_06_27\DELL.parquet
2025-06-27 21:36:23,867 - downloader - INFO - OHLC download completed: 20 successful, 0 failed
2025-06-27 21:36:23,875 - downloader - INFO - Combined close DataFrame shape: (447, 20)
