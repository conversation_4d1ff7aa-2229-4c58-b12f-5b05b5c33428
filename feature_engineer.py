from dataclasses import dataclass
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List
from project_settings.config import PROCESSED_DIR

@dataclass(frozen=True)
class FeatureResult:
    returns: Dict[str, pd.DataFrame]
    sma: Dict[str, pd.DataFrame]
    rank: Dict[str, pd.DataFrame]
    rs_timeseries: pd.DataFrame
    snapshot: pd.DataFrame
    timeseries_file: Path
    snapshot_file: Path

class FeatureEngineer:
    """
    Generates multi-horizon returns, SMAs, cross-sectional ranks, and RS score.

    Args:
      close_horizons: List of strings, e.g. ['return_1d','return_20d']
      rs_horizons: Subset of close_horizons used in RS score.
      weights: Weights dict for rs_horizons.
      sma_windows: List of int windows for SMA.
    """
    def __init__(
        self,
        close_horizons: List[str] = None,
        rs_horizons: List[str] = None,
        weights: Dict[str, float] = None,
        sma_windows: List[int] = None
    ):
        self.close_horizons = close_horizons or ['return_1d','return_20d','return_22d','return_200d']
        self.rs_horizons = rs_horizons or ['return_20d','return_22d','return_200d']
        default_w = {h: 1/len(self.rs_horizons) for h in self.rs_horizons}
        self.weights = weights or default_w
        self.sma_windows = sma_windows or [5,10,20,120]

    def _compute_returns(self, close_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        return {
            h: close_df.pct_change(int(h.split('_')[1].replace('d','')))
            for h in self.close_horizons
        }

    def _compute_sma(self, close_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:

        def ffill_interior_only(df: pd.DataFrame) -> pd.DataFrame:
            """
            Forward-fill interior NaNs in each column of df,
            but leave any trailing NaNs at the bottom.
            """
            # 1) do the normal forward fill
            filled = df.ffill()

            # 2) build a mask of trailing-NaN positions in the original
            #    - df.isna() is True for all NaNs
            #    - reversing it and doing cummin() yields True only where
            #      *all* subsequent values (in the original) were NaN
            trailing_mask = df.isna()[::-1].cummin()[::-1]

            # 3) re-apply NaN to those trailing spots
            return filled.mask(trailing_mask)

        close_df = ffill_interior_only(close_df)
        return {f'sma_{w}': close_df.rolling(w).mean() for w in self.sma_windows}

    def _compute_ranks(self, returns: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        return {
            h: returns[h].rank(axis=1, pct=True).fillna(0)
            for h in self.rs_horizons
        }

    def _compute_rs_ts(self, ranks: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        total = sum(self.weights.values())
        rs = sum(ranks[h] * (self.weights[h]/total) for h in self.rs_horizons)
        rs.index.name = 'date'
        return rs

    def _build_snapshot(
        self,
        close_df: pd.DataFrame,
        returns: Dict[str, pd.DataFrame],
        sma_dfs: Dict[str, pd.DataFrame],
        rs_ts: pd.DataFrame
    ) -> pd.DataFrame:
        latest = rs_ts.index.max()
        snap = pd.DataFrame({'close': close_df.loc[latest]})
        for h, df in returns.items(): snap[h] = df.loc[latest]
        for name, df in sma_dfs.items(): snap[name] = df.loc[latest]
        snap['rs_score'] = rs_ts.loc[latest]
        snap['data_date'] = latest
        snap.index.name = 'ticker'
        return snap

    def compute(self, close_df: pd.DataFrame) -> FeatureResult:
        r = self._compute_returns(close_df)
        s = self._compute_sma(close_df)
        ranks = self._compute_ranks(r)
        rs_ts = self._compute_rs_ts(ranks)
        snap = self._build_snapshot(close_df, r, s, rs_ts)
        ts = datetime.now().strftime('%Y%m%d_%H%M%S')
        f_ts = PROCESSED_DIR / f'rs_timeseries_{ts}.parquet'
        f_snap = PROCESSED_DIR / f'features_{ts}.parquet'
        rs_ts.to_parquet(f_ts, engine='fastparquet')
        snap.to_parquet(f_snap, engine='fastparquet')
        return FeatureResult(r, s, ranks, rs_ts, snap, f_ts, f_snap)