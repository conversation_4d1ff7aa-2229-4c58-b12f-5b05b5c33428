#!/usr/bin/env python3
"""
Test script to verify the integration of the relative strength module 
with the feature engineer.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add the backend directory to the path
sys.path.append('backend')
sys.path.append('.')

from backend.feature_engineer import FeatureEngineer

def create_test_data():
    """Create test data for integration testing."""
    print("📊 Creating test data...")
    
    # Create 10 tickers, 30 days
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    tickers = ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'TSLA', 'META', 'AMZN', 'NFLX', 'AMD', 'INTC']
    
    # Create metadata with themes and subthemes
    metadata = pd.DataFrame({
        'Theme': ['Tech', 'Tech', 'Tech', 'Tech', 'Auto', 'Tech', 'Tech', 'Tech', 'Tech', 'Tech'],
        'Subtheme': ['Hardware', 'Software', 'Search', 'AI', 'EV', 'Social', 'Cloud', 'Streaming', 'Chips', 'Chips']
    }, index=tickers)
    
    # Create price data with some realistic patterns
    np.random.seed(42)
    
    # Base prices
    base_prices = {
        'AAPL': 150, 'MSFT': 300, 'GOOGL': 120, 'NVDA': 400, 'TSLA': 200,
        'META': 250, 'AMZN': 100, 'NFLX': 350, 'AMD': 80, 'INTC': 50
    }
    
    # Generate price series
    price_data = {}
    for ticker in tickers:
        base_price = base_prices[ticker]
        # Random walk with slight upward trend
        returns = np.random.normal(0.001, 0.02, len(dates))
        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        price_data[ticker] = prices
    
    close_df = pd.DataFrame(price_data, index=dates)
    
    print(f"✅ Created test data:")
    print(f"   Close prices shape: {close_df.shape}")
    print(f"   Metadata shape: {metadata.shape}")
    print(f"   Themes: {metadata['Theme'].unique()}")
    print(f"   Subthemes: {metadata['Subtheme'].unique()}")
    
    return close_df, metadata

def test_rs_computation_only():
    """Test just the RS computation without file I/O."""
    print("🚀 TESTING RS COMPUTATION INTEGRATION")
    print("=" * 60)

    # Create test data
    close_df, metadata = create_test_data()

    # Test the RS computation directly
    print(f"\n📊 TEST: RS Computation with metadata")
    print("-" * 40)

    fe = FeatureEngineer(
        close_horizons=['return_1d', 'return_5d', 'return_20d'],
        rs_horizons=['return_5d', 'return_20d'],
        weights={'return_5d': 0.4, 'return_20d': 0.6},
        sma_windows=[5, 20]
    )

    try:
        # Test individual components
        print("🔄 Testing individual components...")

        # 1. Test returns computation
        returns = fe._compute_returns(close_df)
        print(f"✅ Returns computed: {list(returns.keys())}")

        # 2. Test SMA computation
        sma = fe._compute_sma(close_df)
        print(f"✅ SMA computed: {list(sma.keys())}")

        # 3. Test RS computation
        rs_scores = fe._compute_rs_scores(returns, metadata)
        if rs_scores is not None:
            print(f"✅ RS scores computed: {list(rs_scores.keys())}")

            # Validate RS scores
            for rs_type, rs_df in rs_scores.items():
                if rs_type in ['global', 'theme', 'subtheme']:
                    min_val = rs_df.min().min()
                    max_val = rs_df.max().max()
                    in_range = 0 <= min_val <= max_val <= 1
                    print(f"   {rs_type} range: [{min_val:.4f}, {max_val:.4f}] {'✅' if in_range else '❌'}")

            # Test snapshot creation (without saving)
            rs_ts = rs_scores['global']  # Use global RS as main time series
            latest = rs_ts.index.max()

            snap = pd.DataFrame({'close': close_df.loc[latest]})
            for h, df in returns.items():
                snap[h] = df.loc[latest]
            for name, df in sma.items():
                snap[name] = df.loc[latest]
            snap['rs_score'] = rs_ts.loc[latest]  # Legacy global RS score

            # Add enhanced RS scores
            snap['rs_global'] = rs_scores['global'].loc[latest]
            snap['rs_theme'] = rs_scores['theme'].loc[latest]
            snap['rs_subtheme'] = rs_scores['subtheme'].loc[latest]

            # Add metadata
            snap = snap.join(metadata[['Theme', 'Subtheme']], how='left')

            snap['data_date'] = latest
            snap.index.name = 'ticker'

            print(f"✅ Snapshot created:")
            print(f"   Shape: {snap.shape}")
            print(f"   Columns: {list(snap.columns)}")

            # Show sample results
            print(f"\n📈 Sample RS scores for latest date ({latest.strftime('%Y-%m-%d')}):")
            sample_tickers = ['AAPL', 'NVDA', 'TSLA']
            for ticker in sample_tickers:
                if ticker in snap.index:
                    global_score = snap.loc[ticker, 'rs_global']
                    theme_score = snap.loc[ticker, 'rs_theme']
                    subtheme_score = snap.loc[ticker, 'rs_subtheme']
                    theme = snap.loc[ticker, 'Theme']
                    subtheme = snap.loc[ticker, 'Subtheme']

                    print(f"   {ticker} ({theme}/{subtheme}):")
                    print(f"     Global: {global_score:.4f}, Theme: {theme_score:.4f}, Subtheme: {subtheme_score:.4f}")

            return True
        else:
            print("❌ RS scores not computed")
            return False

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_engineer_integration():
    """Test the feature engineer with RS integration."""
    # First test just the computation
    if not test_rs_computation_only():
        return False
    
    print(f"\n🎉 ALL TESTS PASSED!")
    print("=" * 60)
    print("✅ Feature Engineer successfully integrated with RS module")
    print("✅ Enhanced RS scores computed correctly")
    print("✅ All RS scores in valid 0-1 range")
    print("✅ Theme and subtheme scoring working properly")

    return True

if __name__ == "__main__":
    try:
        success = test_feature_engineer_integration()
        if success:
            print("\n🎉 Integration test completed successfully!")
        else:
            print("\n❌ Integration test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Integration test crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
