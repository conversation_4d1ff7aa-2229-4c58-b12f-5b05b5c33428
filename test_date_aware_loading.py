#!/usr/bin/env python3
"""
Test the new date-aware RS data loading functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
from datetime import datetime, timedelta

# Add paths for imports
sys.path.append('.')
sys.path.append('backend')

from relative_strength.utils import save_rs_data, load_rs_data, list_available_rs_dates
from relative_strength.calculator import RelativeStrengthCalculator
from relative_strength.analyzer import RSAnaly<PERSON>

def create_test_rs_data_for_multiple_dates():
    """Create test RS data for multiple dates."""
    print("📊 Creating test RS data for multiple dates...")
    
    # Create test data
    dates = pd.date_range('2024-01-01', periods=5, freq='D')
    tickers = ['AAPL', 'MSFT', 'GOOGL']
    
    metadata = pd.DataFrame({
        'Theme': ['Tech', 'Tech', 'Tech'],
        'Subtheme': ['Hardware', 'Software', 'Search']
    }, index=tickers)
    
    # Create returns data
    np.random.seed(42)
    returns_data = {}
    for horizon in ['return_5d', 'return_20d']:
        returns_matrix = np.random.normal(0.01, 0.02, (5, 3))
        returns_data[horizon] = pd.DataFrame(
            returns_matrix, index=dates, columns=tickers
        )
    
    # Compute RS scores
    rs_calc = RelativeStrengthCalculator(
        horizons=['return_5d', 'return_20d'],
        weights={'return_5d': 0.4, 'return_20d': 0.6}
    )
    
    rs_scores = rs_calc.compute_all_rs_scores(returns_data, metadata)
    
    # Save data for multiple dates
    test_dates = ['2025_06_27', '2025_06_28', '2025_06_29']
    saved_files_by_date = {}
    
    for test_date in test_dates:
        print(f"\n💾 Saving RS data for {test_date}...")
        
        # Create custom directory for this test date
        from project_settings.config import RS_BASE_DIR
        test_date_dir = RS_BASE_DIR / test_date
        
        # Save with custom timestamp to differentiate
        timestamp = f"{test_date.replace('_', '')}_{test_date[-2:]}{test_date[-2:]}00"
        
        # Modify data slightly for each date to make them different
        modified_rs_scores = {}
        for rs_type, rs_df in rs_scores.items():
            if rs_type in ['global', 'theme', 'subtheme']:
                # Add small random variation for each date
                variation = np.random.normal(0, 0.01, rs_df.shape)
                modified_rs_scores[rs_type] = rs_df + variation
                modified_rs_scores[rs_type] = modified_rs_scores[rs_type].clip(0, 1)  # Keep in 0-1 range
            else:
                modified_rs_scores[rs_type] = rs_df
        
        saved_files = save_rs_data(modified_rs_scores, test_date_dir, timestamp)
        saved_files_by_date[test_date] = saved_files
        
        print(f"✅ Saved {len(saved_files)} files for {test_date}")
    
    return test_dates, metadata

def test_date_aware_loading():
    """Test the date-aware loading functionality."""
    print("\n🧪 TESTING DATE-AWARE RS DATA LOADING")
    print("=" * 60)
    
    # Create test data for multiple dates
    test_dates, metadata = create_test_rs_data_for_multiple_dates()
    
    # Test 1: List available dates
    print(f"\n📅 TEST 1: List available dates")
    print("-" * 30)
    
    available_dates = list_available_rs_dates()
    print(f"✅ Found {len(available_dates)} available dates")
    
    # Test 2: Load without date (should show warning)
    print(f"\n⚠️  TEST 2: Load without date specification (should show warning)")
    print("-" * 30)
    
    try:
        rs_scores_no_date = load_rs_data()  # Should show warning
        print(f"✅ Loaded data with warning (shape: {rs_scores_no_date['global'].shape})")
    except Exception as e:
        print(f"❌ Failed to load without date: {e}")
    
    # Test 3: Load with specific dates
    print(f"\n📅 TEST 3: Load with specific dates")
    print("-" * 30)
    
    for test_date in test_dates:
        try:
            print(f"\n🔍 Loading data for {test_date}:")
            rs_scores_dated = load_rs_data(date=test_date)
            print(f"✅ Successfully loaded data for {test_date}")
            print(f"   Global RS shape: {rs_scores_dated['global'].shape}")
            
        except Exception as e:
            print(f"❌ Failed to load data for {test_date}: {e}")
    
    # Test 4: Load with invalid date format
    print(f"\n❌ TEST 4: Load with invalid date format")
    print("-" * 30)
    
    try:
        rs_scores_invalid = load_rs_data(date="2025-06-28")  # Wrong format
        print(f"❌ Should have failed but didn't!")
    except ValueError as e:
        print(f"✅ Correctly rejected invalid date format: {e}")
    except Exception as e:
        print(f"⚠️  Unexpected error: {e}")
    
    # Test 5: Load with non-existent date
    print(f"\n❌ TEST 5: Load with non-existent date")
    print("-" * 30)
    
    try:
        rs_scores_missing = load_rs_data(date="2020_01_01")
        print(f"❌ Should have failed but didn't!")
    except FileNotFoundError as e:
        print(f"✅ Correctly handled missing date: {e}")
    except Exception as e:
        print(f"⚠️  Unexpected error: {e}")
    
    # Test 6: Create RSAnalyzer from date
    print(f"\n🔬 TEST 6: Create RSAnalyzer from date")
    print("-" * 30)
    
    try:
        analyzer = RSAnalyzer.from_date(test_dates[0], metadata)
        snapshot = analyzer.get_latest_snapshot()
        print(f"✅ Created RSAnalyzer from date {test_dates[0]}")
        print(f"   Snapshot shape: {snapshot.shape}")
        print(f"   RS columns: {[col for col in snapshot.columns if col.startswith('rs_')]}")
    except Exception as e:
        print(f"❌ Failed to create RSAnalyzer from date: {e}")
    
    # Test 7: Show clear loading messages
    print(f"\n📋 TEST 7: Demonstrate clear loading messages")
    print("-" * 30)
    
    print("Example of loading with explicit date:")
    try:
        rs_scores_clear = load_rs_data(date=test_dates[1])
        print("✅ Clear loading messages displayed above")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    return True

def cleanup_test_data():
    """Clean up test data."""
    try:
        from project_settings.config import RS_BASE_DIR
        test_dates = ['2025_06_27', '2025_06_28', '2025_06_29']
        
        for test_date in test_dates:
            test_date_dir = RS_BASE_DIR / test_date
            if test_date_dir.exists():
                for file in test_date_dir.glob("rs_*_timeseries_*.parquet"):
                    file.unlink()
                    print(f"🧹 Cleaned up {file.name}")
                
                # Remove directory if empty
                try:
                    test_date_dir.rmdir()
                    print(f"🧹 Removed empty directory {test_date}")
                except OSError:
                    pass  # Directory not empty, that's ok
                    
    except Exception as e:
        print(f"⚠️  Cleanup warning: {e}")

if __name__ == "__main__":
    try:
        success = test_date_aware_loading()
        if success:
            print("\n🎉 Date-aware loading test completed successfully!")
            print("\n📋 Key Features Demonstrated:")
            print("✅ Date-specific loading with clear folder indication")
            print("✅ Warning when no date specified")
            print("✅ Validation of date format (YYYY_MM_DD)")
            print("✅ Clear error messages for missing dates")
            print("✅ List of available dates")
            print("✅ RSAnalyzer.from_date() class method")
        else:
            print("\n❌ Date-aware loading test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        cleanup_test_data()
