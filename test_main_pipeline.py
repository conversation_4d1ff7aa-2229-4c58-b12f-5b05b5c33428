#!/usr/bin/env python3
"""
Test the main pipeline with enhanced RS integration.
"""

import pandas as pd
import numpy as np
from pathlib import Path

def create_test_csv():
    """Create a test CSV file for the pipeline."""
    print("📝 Creating test CSV file...")
    
    # Create test data
    test_data = {
        'ticker': ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'TSLA', 'META', 'AMZN'],
        'Theme': ['Tech', 'Tech', 'Tech', 'Tech', 'Auto', 'Tech', 'Tech'],
        'Subtheme': ['Hardware', 'Software', 'Search', 'AI', 'EV', 'Social', 'Cloud']
    }
    
    df = pd.DataFrame(test_data)
    df.to_csv('test_tickers.csv', index=False)
    
    print(f"✅ Created test_tickers.csv with {len(df)} tickers")
    return 'test_tickers.csv'

def test_main_pipeline():
    """Test the main pipeline with enhanced RS."""
    print("🚀 TESTING MAIN PIPELINE WITH ENHANCED RS")
    print("=" * 60)
    
    # Create test CSV
    csv_file = create_test_csv()
    
    try:
        # Import and run main function
        from main import main
        
        print(f"\n🔄 Running main pipeline...")
        dashboard, output_path = main(
            input_csv=csv_file,
            lookback=60,  # Shorter for testing
            close_horizons=['return_1d', 'return_5d', 'return_20d'],
            rs_horizons=['return_5d', 'return_20d'],
            weights={'return_5d': 0.4, 'return_20d': 0.6},
            sma_windows=[5, 20]
        )
        
        print(f"\n✅ Pipeline completed successfully!")
        print(f"📊 Dashboard shape: {dashboard.shape}")
        print(f"📁 Output file: {output_path}")
        
        # Check enhanced RS columns
        rs_columns = [col for col in dashboard.columns if col.startswith('rs_')]
        print(f"📈 RS columns: {rs_columns}")
        
        # Show sample results
        if 'rs_global' in dashboard.columns:
            print(f"\n🏆 Top 3 Global RS performers:")
            top_3 = dashboard.nlargest(3, 'rs_global')[['Theme', 'Subtheme', 'rs_global', 'rs_theme', 'rs_subtheme']]
            print(top_3.round(4))
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test file
        if Path(csv_file).exists():
            Path(csv_file).unlink()
            print(f"🧹 Cleaned up {csv_file}")

if __name__ == "__main__":
    try:
        success = test_main_pipeline()
        if success:
            print("\n🎉 Main pipeline test completed successfully!")
        else:
            print("\n❌ Main pipeline test failed!")
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()
