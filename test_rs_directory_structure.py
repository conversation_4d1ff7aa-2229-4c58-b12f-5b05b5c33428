#!/usr/bin/env python3
"""
Test the new RS directory structure integration.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add paths for imports
sys.path.append('.')
sys.path.append('backend')

from project_settings.config import get_rs_dir, RS_BASE_DIR
from relative_strength.utils import save_rs_data, load_rs_data
from relative_strength.calculator import RelativeStrengthCalculator

def test_rs_directory_structure():
    """Test that RS data is saved to the correct directory structure."""
    print("🧪 TESTING RS DIRECTORY STRUCTURE")
    print("=" * 50)
    
    # Test 1: Check config directories
    print("\n📁 TEST 1: Check config directories")
    print("-" * 30)
    
    rs_base_dir = RS_BASE_DIR
    rs_today_dir = get_rs_dir()
    
    print(f"✅ RS base directory: {rs_base_dir}")
    print(f"✅ RS today directory: {rs_today_dir}")
    print(f"✅ RS base exists: {rs_base_dir.exists()}")
    print(f"✅ RS today exists: {rs_today_dir.exists()}")
    
    # Test 2: Create test RS data
    print("\n📊 TEST 2: Create test RS data")
    print("-" * 30)
    
    # Create simple test data
    dates = pd.date_range('2024-01-01', periods=5, freq='D')
    tickers = ['AAPL', 'MSFT', 'GOOGL']
    
    # Create metadata
    metadata = pd.DataFrame({
        'Theme': ['Tech', 'Tech', 'Tech'],
        'Subtheme': ['Hardware', 'Software', 'Search']
    }, index=tickers)
    
    # Create returns data
    np.random.seed(42)
    returns_data = {}
    for horizon in ['return_5d', 'return_20d']:
        returns_matrix = np.random.normal(0.01, 0.02, (5, 3))
        returns_data[horizon] = pd.DataFrame(
            returns_matrix, index=dates, columns=tickers
        )
    
    print(f"✅ Created test data: {len(tickers)} tickers, {len(dates)} dates")
    
    # Test 3: Compute RS scores
    print("\n🎯 TEST 3: Compute RS scores")
    print("-" * 30)
    
    rs_calc = RelativeStrengthCalculator(
        horizons=['return_5d', 'return_20d'],
        weights={'return_5d': 0.4, 'return_20d': 0.6}
    )
    
    rs_scores = rs_calc.compute_all_rs_scores(returns_data, metadata)
    print(f"✅ RS scores computed: {list(rs_scores.keys())}")
    
    # Test 4: Save to configured directory
    print("\n💾 TEST 4: Save to configured RS directory")
    print("-" * 30)
    
    saved_files = save_rs_data(rs_scores)
    
    print(f"✅ Saved {len(saved_files)} files:")
    for rs_type, filepath in saved_files.items():
        print(f"   {rs_type}: {filepath}")
        print(f"     Exists: {filepath.exists()}")
        print(f"     Parent: {filepath.parent}")
        
        # Verify it's in the correct directory structure
        expected_parent = get_rs_dir()
        if filepath.parent == expected_parent:
            print(f"     ✅ Correct directory structure")
        else:
            print(f"     ❌ Wrong directory: expected {expected_parent}")
    
    # Test 5: Load from configured directory
    print("\n📂 TEST 5: Load from configured RS directory")
    print("-" * 30)
    
    try:
        loaded_rs_scores = load_rs_data()
        print(f"✅ Loaded RS data:")
        for rs_type, rs_df in loaded_rs_scores.items():
            print(f"   {rs_type}: {rs_df.shape}")
        
        # Verify data integrity
        data_matches = all(
            rs_scores[rs_type].equals(loaded_rs_scores[rs_type])
            for rs_type in ['global', 'theme', 'subtheme']
        )
        print(f"✅ Data integrity: {'Verified' if data_matches else 'Failed'}")
        
    except Exception as e:
        print(f"❌ Load failed: {e}")
        return False
    
    # Test 6: Check directory structure
    print("\n📁 TEST 6: Verify directory structure")
    print("-" * 30)
    
    print(f"Expected structure:")
    print(f"data/")
    print(f"├── relative_strength/")
    print(f"│   └── {get_rs_dir().name}/")
    print(f"│       ├── rs_global_timeseries_*.parquet")
    print(f"│       ├── rs_theme_timeseries_*.parquet")
    print(f"│       └── rs_subtheme_timeseries_*.parquet")
    
    print(f"\nActual structure:")
    rs_dir = get_rs_dir()
    if rs_dir.exists():
        files = list(rs_dir.glob("*.parquet"))
        print(f"📁 {rs_dir}:")
        for file in sorted(files):
            print(f"   📄 {file.name}")
        print(f"✅ Found {len(files)} RS files in correct location")
    else:
        print(f"❌ RS directory not found: {rs_dir}")
        return False
    
    print(f"\n🎉 ALL TESTS PASSED!")
    print("✅ RS data is correctly saved to data/relative_strength/ structure")
    print("✅ Config-based directory management working")
    print("✅ Save/load functions use configured directories")
    
    return True

def cleanup_test_files():
    """Clean up test files."""
    try:
        rs_dir = get_rs_dir()
        if rs_dir.exists():
            for file in rs_dir.glob("rs_*_timeseries_*.parquet"):
                file.unlink()
                print(f"🧹 Cleaned up {file.name}")
    except Exception as e:
        print(f"⚠️  Cleanup warning: {e}")

if __name__ == "__main__":
    try:
        success = test_rs_directory_structure()
        if success:
            print("\n🎉 Directory structure test completed successfully!")
        else:
            print("\n❌ Directory structure test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        cleanup_test_files()
