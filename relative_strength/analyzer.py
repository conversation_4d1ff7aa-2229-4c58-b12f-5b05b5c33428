"""
Relative Strength Analyzer

Tools for analyzing and interpreting RS scores.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple

class RSAnalyzer:
    """Analyze relative strength scores and generate insights."""
    
    def __init__(self, rs_scores: Dict[str, pd.DataFrame], metadata: pd.DataFrame):
        """
        Initialize analyzer with RS scores and metadata.
        
        Args:
            rs_scores: Dict with 'global', 'theme', 'subtheme' RS DataFrames
            metadata: DataFrame with Theme and Subtheme columns
        """
        self.rs_scores = rs_scores
        self.metadata = metadata
        self._validate_data()
    
    def _validate_data(self):
        """Validate input data consistency."""
        required_keys = {'global', 'theme', 'subtheme'}
        if not required_keys.issubset(self.rs_scores.keys()):
            raise ValueError(f"RS scores must contain keys: {required_keys}")
        
        # Check shapes match
        shapes = {k: v.shape for k, v in self.rs_scores.items()}
        if len(set(shapes.values())) > 1:
            raise ValueError(f"RS score shapes don't match: {shapes}")
    
    def get_latest_snapshot(self) -> pd.DataFrame:
        """
        Get latest RS scores for all tickers.
        
        Returns:
            DataFrame with latest RS scores and metadata
        """
        latest_date = self.rs_scores['global'].index.max()
        
        snapshot = pd.DataFrame(index=self.rs_scores['global'].columns)
        snapshot.index.name = 'ticker'
        
        # Add RS scores
        snapshot['rs_global'] = self.rs_scores['global'].loc[latest_date]
        snapshot['rs_theme'] = self.rs_scores['theme'].loc[latest_date]
        snapshot['rs_subtheme'] = self.rs_scores['subtheme'].loc[latest_date]
        
        # Add metadata
        snapshot = snapshot.join(self.metadata[['Theme', 'Subtheme']], how='left')
        
        # Add percentile interpretations
        snapshot['global_percentile'] = snapshot['rs_global'] * 100
        snapshot['theme_percentile'] = snapshot['rs_theme'] * 100
        snapshot['subtheme_percentile'] = snapshot['rs_subtheme'] * 100
        
        snapshot['data_date'] = latest_date
        
        return snapshot
    
    def get_top_performers(self, rs_type: str = 'global', n: int = 10) -> pd.DataFrame:
        """
        Get top N performers by RS type.
        
        Args:
            rs_type: 'global', 'theme', or 'subtheme'
            n: Number of top performers to return
            
        Returns:
            DataFrame with top performers
        """
        snapshot = self.get_latest_snapshot()
        rs_col = f'rs_{rs_type}'
        
        if rs_col not in snapshot.columns:
            raise ValueError(f"Invalid rs_type: {rs_type}")
        
        return snapshot.nlargest(n, rs_col)[
            ['Theme', 'Subtheme', rs_col, f'{rs_type}_percentile']
        ]
    
    def analyze_by_theme(self) -> pd.DataFrame:
        """
        Analyze RS performance by theme.
        
        Returns:
            DataFrame with theme-level statistics
        """
        snapshot = self.get_latest_snapshot()
        
        theme_stats = snapshot.groupby('Theme').agg({
            'rs_global': ['mean', 'std', 'min', 'max', 'count'],
            'rs_theme': ['mean', 'std'],
            'rs_subtheme': ['mean', 'std']
        }).round(4)
        
        # Flatten column names
        theme_stats.columns = ['_'.join(col) for col in theme_stats.columns]
        theme_stats = theme_stats.rename(columns={'rs_global_count': 'ticker_count'})
        
        return theme_stats.sort_values('rs_global_mean', ascending=False)
    
    def find_divergences(self, threshold: float = 0.3) -> pd.DataFrame:
        """
        Find tickers with divergent RS scores.
        
        Args:
            threshold: Divergence threshold (0-1)
            
        Returns:
            DataFrame with divergent tickers
        """
        snapshot = self.get_latest_snapshot()
        
        # High theme RS but low global RS
        divergent = snapshot[
            (snapshot['rs_theme'] > (1 - threshold)) & 
            (snapshot['rs_global'] < threshold)
        ]
        
        if len(divergent) > 0:
            return divergent[
                ['Theme', 'Subtheme', 'rs_global', 'rs_theme', 'rs_subtheme',
                 'global_percentile', 'theme_percentile', 'subtheme_percentile']
            ].sort_values('rs_theme', ascending=False)
        
        return pd.DataFrame()
    
    def get_ticker_timeseries(self, ticker: str) -> pd.DataFrame:
        """
        Get complete time series for a specific ticker.
        
        Args:
            ticker: Ticker symbol
            
        Returns:
            DataFrame with all three RS time series
        """
        if ticker not in self.rs_scores['global'].columns:
            raise ValueError(f"Ticker {ticker} not found in RS data")
        
        result = pd.DataFrame(index=self.rs_scores['global'].index)
        result['rs_global'] = self.rs_scores['global'][ticker]
        result['rs_theme'] = self.rs_scores['theme'][ticker]
        result['rs_subtheme'] = self.rs_scores['subtheme'][ticker]
        
        # Add rolling statistics
        result['global_ma_5'] = result['rs_global'].rolling(5).mean()
        result['global_ma_20'] = result['rs_global'].rolling(20).mean()
        result['global_volatility'] = result['rs_global'].rolling(10).std()
        
        return result.dropna(how='all')
    
    def compare_tickers(self, tickers: List[str]) -> pd.DataFrame:
        """
        Compare multiple tickers across all RS types.
        
        Args:
            tickers: List of ticker symbols
            
        Returns:
            DataFrame comparing tickers
        """
        snapshot = self.get_latest_snapshot()
        
        comparison = snapshot.loc[tickers, [
            'Theme', 'Subtheme', 'rs_global', 'rs_theme', 'rs_subtheme',
            'global_percentile', 'theme_percentile', 'subtheme_percentile'
        ]].copy()
        
        # Add relative scores within comparison group
        comparison['global_score_vs_group'] = comparison['rs_global'].rank(pct=True)
        comparison['theme_score_vs_group'] = comparison['rs_theme'].rank(pct=True)
        comparison['subtheme_score_vs_group'] = comparison['rs_subtheme'].rank(pct=True)
        
        return comparison.sort_values('rs_global', ascending=False)
    
    def generate_summary_report(self) -> str:
        """Generate a comprehensive summary report."""
        snapshot = self.get_latest_snapshot()
        
        report = []
        report.append("📊 RELATIVE STRENGTH SUMMARY REPORT")
        report.append("=" * 50)
        report.append(f"Analysis Date: {snapshot['data_date'].iloc[0]}")
        report.append(f"Total Tickers: {len(snapshot)}")
        report.append(f"Themes: {snapshot['Theme'].nunique()}")
        report.append(f"Subthemes: {snapshot['Subtheme'].nunique()}")
        report.append("")
        
        # Top performers
        report.append("🏆 TOP 5 GLOBAL PERFORMERS:")
        top_global = self.get_top_performers('global', 5)
        report.append(top_global.to_string())
        report.append("")
        
        # Theme analysis
        report.append("🎯 THEME PERFORMANCE:")
        theme_stats = self.analyze_by_theme()
        key_cols = ['rs_global_mean', 'ticker_count', 'rs_global_std']
        report.append(theme_stats[key_cols].to_string())
        report.append("")
        
        # Divergences
        divergences = self.find_divergences()
        if not divergences.empty:
            report.append("🔍 RS DIVERGENCES (High Theme, Low Global):")
            report.append(divergences.head(3)[
                ['Theme', 'rs_global', 'rs_theme', 'global_percentile', 'theme_percentile']
            ].to_string())
            report.append("")
        
        # Summary statistics
        report.append("📈 RS SCORE STATISTICS:")
        for rs_type in ['global', 'theme', 'subtheme']:
            col = f'rs_{rs_type}'
            stats = snapshot[col].describe()
            report.append(f"{rs_type.capitalize()}: Mean={stats['mean']:.3f}, "
                         f"Std={stats['std']:.3f}, Range=[{stats['min']:.3f}, {stats['max']:.3f}]")
        
        return "\n".join(report)
