# Corrected Relative Strength Approach

## The Correct Two-Step Process

### Step 1: Compute Weighted Returns
**For each ticker, combine multiple return horizons using weights:**

```
Weighted Return = w₁ × return_1d + w₂ × return_20d + w₃ × return_60d + ...
```

**Example:**
```
AAPL_weighted_return = 0.2 × AAPL_1d + 0.5 × AAPL_20d + 0.3 × AAPL_60d
```

### Step 2: Rank Weighted Returns Within Groupings
**Rank the weighted returns within different groupings per day:**

1. **Global RS**: Rank vs ALL tickers
2. **Theme RS**: Rank vs SAME theme tickers  
3. **Subtheme RS**: Rank vs SAME subtheme tickers

## Implementation

```python
class RelativeStrengthCalculator:
    def compute_weighted_returns(self, returns):
        """Step 1: Compute weighted returns"""
        weighted_returns = sum(
            returns[horizon] * self.weights[horizon] 
            for horizon in self.horizons
        )
        return weighted_returns
    
    def compute_all_rs_scores(self, returns, metadata):
        """Step 2: Rank within groupings"""
        # Step 1: Compute weighted returns once
        weighted_returns = self.compute_weighted_returns(returns)
        
        # Step 2: Rank within different groupings
        global_rs = weighted_returns.rank(axis=1, pct=True)
        theme_rs = self._rank_within_groups(weighted_returns, metadata, 'Theme')
        subtheme_rs = self._rank_within_groups(weighted_returns, metadata, 'Subtheme')
        
        return {
            'global': global_rs,
            'theme': theme_rs, 
            'subtheme': subtheme_rs
        }
```

## Validation Results

### ✅ Perfect Correlation
Higher weighted returns lead to higher RS scores:
- 2024-01-01: correlation = 0.999687 ✅
- 2024-01-02: correlation = 0.999800 ✅  
- 2024-01-03: correlation = 0.999535 ✅

### ✅ Correct Value Ranges
All RS scores in 0-1 range where 1=best, 0=worst:
- Global: [0.1667, 1.0000] ✅
- Theme: [0.3333, 1.0000] ✅
- Subtheme: [0.5000, 1.0000] ✅

### ✅ Correct Rankings
Theme rankings work correctly within groups:
- Theme_X (A1 > A2 > A3): {A1: 1.0, A2: 0.667, A3: 0.333} ✅
- Theme_Y (B1 > B2 > B3): {B1: 1.0, B2: 0.667, B3: 0.333} ✅

## Example Output

### Weighted Returns (Step 1)
```
Date        A1      A2      A3      B1      B2      B3
2024-01-01  0.0705  0.0513  0.0311  0.0614  0.0407  0.0201
```

### Global RS (Step 2a: Rank vs ALL tickers)
```
Ticker  Weighted Ret  Global RS  Rank
A1      0.0705        1.0000     1/6
B1      0.0614        0.8333     2/6  
A2      0.0513        0.6667     3/6
B2      0.0407        0.5000     4/6
A3      0.0311        0.3333     5/6
B3      0.0201        0.1667     6/6
```

### Theme RS (Step 2b: Rank vs SAME THEME)
```
Theme_X (A1, A2, A3):
A1: 1.0000 (1st in Theme_X)
A2: 0.6667 (2nd in Theme_X)  
A3: 0.3333 (3rd in Theme_X)

Theme_Y (B1, B2, B3):
B1: 1.0000 (1st in Theme_Y)
B2: 0.6667 (2nd in Theme_Y)
B3: 0.3333 (3rd in Theme_Y)
```

## Key Benefits

### 1. **Mathematically Sound**
- Weighted returns properly combine multiple horizons
- Percentile ranking ensures 0-1 scale where higher=better

### 2. **Efficient Computation**
- Compute weighted returns once
- Reuse for all three ranking types

### 3. **Clear Interpretation**
- 1.0 = Best performer in group
- 0.5 = Median performer in group
- 0.0 = Worst performer in group

### 4. **Multi-Level Analysis**
- Global RS: Market positioning
- Theme RS: Sector leadership
- Subtheme RS: Niche dominance

## Usage

### Run Validation
```bash
cd relative_strength
python validate_correct_approach.py
```

### Run Complete Example  
```bash
cd relative_strength
python step_by_step_example.py
```

### Integration
```python
from relative_strength.calculator import RelativeStrengthCalculator

rs_calc = RelativeStrengthCalculator(
    horizons=['return_20d', 'return_60d', 'return_200d'],
    weights={'return_20d': 0.5, 'return_60d': 0.3, 'return_200d': 0.2}
)

rs_scores = rs_calc.compute_all_rs_scores(returns_data, metadata)
```

## Files in Module

```
relative_strength/
├── calculator.py              # Core RS calculation (CORRECTED)
├── analyzer.py               # Analysis tools
├── utils.py                  # Utility functions
├── validate_correct_approach.py  # Validation with simple data
├── step_by_step_example.py   # Complete example
└── CORRECTED_APPROACH.md     # This documentation
```

## Summary

The **corrected approach** ensures that:

1. ✅ **Weighted returns are computed first** (combining horizons)
2. ✅ **Then ranked within respective groupings** per day
3. ✅ **Higher weighted returns → Higher RS scores** (always)
4. ✅ **All RS scores in 0-1 range** where higher=better
5. ✅ **Three time series per ticker** (global, theme, subtheme)

This provides a mathematically sound and interpretable relative strength system!
