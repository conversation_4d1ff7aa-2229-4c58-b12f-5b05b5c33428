# Relative Strength Module

A comprehensive relative strength calculation and analysis module that computes three time series per ticker:

1. **Global RS**: Ticker vs ALL tickers (0-1, higher=better)
2. **Theme RS**: Ticker vs SAME theme tickers (0-1, higher=better)  
3. **Subtheme RS**: Ticker vs SAME subtheme tickers (0-1, higher=better)

## Quick Start

```python
from relative_strength import RelativeStrengthCalculator, RSAnalyzer
from relative_strength.utils import create_fake_data

# Create sample data
returns_data, metadata = create_fake_data(n_tickers=20, n_days=60)

# Compute RS scores
rs_calc = RelativeStrengthCalculator()
rs_scores = rs_calc.compute_all_rs_scores(returns_data, metadata)

# Analyze results
analyzer = RSAnalyzer(rs_scores, metadata)
top_performers = analyzer.get_top_performers('global', 10)
print(top_performers)
```

## Module Structure

```
relative_strength/
├── __init__.py           # Module exports
├── calculator.py         # Core RS calculation engine
├── analyzer.py          # Analysis and interpretation tools
├── utils.py             # Utility functions
├── integration.py       # Integration with existing codebase
├── step_by_step_example.py  # Complete example with fake data
└── README.md            # This file
```

## Core Classes

### RelativeStrengthCalculator

Main calculation engine that computes RS scores.

```python
rs_calc = RelativeStrengthCalculator(
    horizons=['return_20d', 'return_60d', 'return_200d'],
    weights={'return_20d': 0.5, 'return_60d': 0.3, 'return_200d': 0.2}
)

rs_scores = rs_calc.compute_all_rs_scores(returns_data, metadata)
```

### RSAnalyzer

Analysis and interpretation tools.

```python
analyzer = RSAnalyzer(rs_scores, metadata)

# Get top performers
top_global = analyzer.get_top_performers('global', 10)
top_theme = analyzer.get_top_performers('theme', 10)

# Analyze by theme
theme_stats = analyzer.analyze_by_theme()

# Find divergences
divergences = analyzer.find_divergences()

# Track individual ticker
ticker_ts = analyzer.get_ticker_timeseries('AAPL')
```

## Key Features

### ✅ Three RS Time Series Per Ticker
- **Global RS**: Percentile rank vs entire universe
- **Theme RS**: Percentile rank vs same theme peers
- **Subtheme RS**: Percentile rank vs direct competitors

### ✅ Consistent 0-1 Scale
- **1.0** = Best performer in group
- **0.5** = Median performer in group
- **0.0** = Worst performer in group

### ✅ Flexible Configuration
- Custom return horizons
- Weighted combination of horizons
- Configurable themes and subthemes

### ✅ Comprehensive Analysis
- Top performer identification
- Theme performance analysis
- Divergence detection
- Individual ticker tracking

## Data Requirements

### Returns Data
Dictionary with return DataFrames:
```python
returns_data = {
    'return_20d': DataFrame,  # 20-day returns (dates x tickers)
    'return_60d': DataFrame,  # 60-day returns (dates x tickers)
    'return_200d': DataFrame  # 200-day returns (dates x tickers)
}
```

### Metadata
DataFrame with theme classifications:
```python
metadata = pd.DataFrame({
    'Theme': ['AI', 'Fintech', 'AI', ...],
    'Subtheme': ['Machine Learning', 'Payments', 'Computer Vision', ...]
}, index=['AAPL', 'MSFT', 'GOOGL', ...])
```

## Output Data

### RS Scores
Dictionary with three time series DataFrames:
```python
rs_scores = {
    'global': DataFrame,    # Global RS (dates x tickers)
    'theme': DataFrame,     # Theme RS (dates x tickers)
    'subtheme': DataFrame   # Subtheme RS (dates x tickers)
}
```

### Enhanced Snapshot
Latest RS scores with metadata:
```python
snapshot = analyzer.get_latest_snapshot()
# Columns: rs_global, rs_theme, rs_subtheme, Theme, Subtheme, 
#          global_percentile, theme_percentile, subtheme_percentile
```

## Examples

### Run Complete Example
```bash
cd relative_strength
python step_by_step_example.py
```

This demonstrates:
- Creating fake data
- Computing RS scores
- Analyzing results
- Saving/loading data
- Generating reports

### Integration Example
```python
from relative_strength.integration import compute_enhanced_rs_scores

# Compute RS scores for existing pipeline
rs_scores = compute_enhanced_rs_scores(
    returns=your_returns_data,
    metadata=your_metadata,
    output_dir=Path('output')
)
```

## Validation

The module includes comprehensive validation:
- RS scores in 0-1 range
- Higher returns → higher RS scores
- Theme/subtheme rankings work correctly
- Time series consistency

Run validation:
```python
from relative_strength.utils import validate_rs_scores
is_valid = validate_rs_scores(rs_scores)
```

## Use Cases

### 1. Stock Selection
- Identify global market leaders (high global RS)
- Find theme leaders (high theme RS)
- Discover niche specialists (high subtheme RS)

### 2. Sector Rotation
- Compare theme performance over time
- Identify rotating sectors
- Time sector allocation

### 3. Divergence Trading
- High theme RS + low global RS = sector rotation candidate
- High subtheme RS + low theme RS = niche opportunity
- Low global RS + high theme RS = avoid (weak in strong sector)

### 4. Risk Management
- Monitor RS deterioration
- Track relative performance trends
- Identify concentration risks

## Performance Tips

1. **Use appropriate horizons**: Match horizons to your strategy timeframe
2. **Weight horizons properly**: More weight on relevant timeframes
3. **Regular updates**: Recompute RS scores with new data
4. **Theme granularity**: Balance specificity vs sample size

## Dependencies

- pandas
- numpy
- pathlib (standard library)
- datetime (standard library)

## Version

1.0.0 - Initial release with core functionality
