"""
Utility functions for relative strength module.
"""

import pandas as pd
from pathlib import Path
from typing import Dict, Optional
from datetime import datetime
import sys
import os

# Add project root to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from project_settings.config import get_rs_dir

def save_rs_data(
    rs_scores: Dict[str, pd.DataFrame],
    output_dir: Optional[Path] = None,
    timestamp: Optional[str] = None
) -> Dict[str, Path]:
    """
    Save RS time series to parquet files in the configured RS directory.

    Args:
        rs_scores: Dict with 'global', 'theme', 'subtheme' DataFrames
        output_dir: Optional directory to save files (defaults to config RS dir)
        timestamp: Optional timestamp string, defaults to current time

    Returns:
        Dict mapping RS type to file path
    """
    if timestamp is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Use configured RS directory if no output_dir specified
    if output_dir is None:
        output_dir = get_rs_dir()

    output_dir.mkdir(parents=True, exist_ok=True)

    saved_files = {}
    for rs_type, rs_df in rs_scores.items():
        if rs_type.endswith('_scores') or rs_type == 'weighted_returns':  # Skip intermediate data
            continue

        filename = f'rs_{rs_type}_timeseries_{timestamp}.parquet'
        filepath = output_dir / filename
        rs_df.to_parquet(filepath, engine='fastparquet')
        saved_files[rs_type] = filepath
        print(f"💾 Saved {rs_type} RS to {filepath}")

    return saved_files

def load_rs_data(
    date: Optional[str] = None,
    data_dir: Optional[Path] = None,
    timestamp: Optional[str] = None
) -> Dict[str, pd.DataFrame]:
    """
    Load RS time series from parquet files.

    Args:
        date: Date string in YYYY_MM_DD format (e.g., '2025_06_28').
              If None, uses today's date and shows warning.
        data_dir: Optional custom directory containing RS files.
                 If None, uses config RS directory for the specified date.
        timestamp: Optional specific timestamp within the date, defaults to latest

    Returns:
        Dict with 'global', 'theme', 'subtheme' DataFrames
    """
    # Determine the data directory
    if data_dir is None:
        if date is None:
            # Use today's date but warn user
            from datetime import datetime
            today_date = datetime.now().strftime('%Y_%m_%d')
            data_dir = get_rs_dir()  # Uses today's date
            print(f"⚠️  WARNING: No date specified! Loading from TODAY'S folder: {data_dir}")
            print(f"   To load from a specific date, use: load_rs_data(date='{today_date}')")
        else:
            # Use specified date
            from datetime import datetime
            try:
                # Validate date format
                datetime.strptime(date, '%Y_%m_%d')
                date_obj = datetime.strptime(date, '%Y_%m_%d')
                data_dir = get_rs_dir(date_obj)
                print(f"📅 Loading RS data from date: {date}")
                print(f"📁 Directory: {data_dir}")
            except ValueError:
                raise ValueError(f"Invalid date format: {date}. Expected format: YYYY_MM_DD (e.g., '2025_06_28')")
    else:
        # Custom directory specified
        print(f"📁 Loading RS data from custom directory: {data_dir}")

    if not data_dir.exists():
        available_dates = []
        rs_base = data_dir.parent if date else get_rs_dir().parent
        if rs_base.exists():
            available_dates = [d.name for d in rs_base.iterdir() if d.is_dir()]

        error_msg = f"RS data directory not found: {data_dir}"
        if available_dates:
            error_msg += f"\nAvailable dates: {sorted(available_dates)}"
        else:
            error_msg += f"\nNo RS data directories found in: {rs_base}"
        raise FileNotFoundError(error_msg)

    rs_scores = {}
    loaded_files = []

    for rs_type in ['global', 'theme', 'subtheme']:
        if timestamp:
            # Load specific timestamp
            filename = f'rs_{rs_type}_timeseries_{timestamp}.parquet'
            filepath = data_dir / filename
        else:
            # Find latest file
            pattern = f'rs_{rs_type}_timeseries_*.parquet'
            files = list(data_dir.glob(pattern))
            if not files:
                raise FileNotFoundError(f"No {rs_type} RS files found in {data_dir}")
            filepath = max(files, key=lambda p: p.stat().st_mtime)

        if not filepath.exists():
            raise FileNotFoundError(f"RS file not found: {filepath}")

        rs_scores[rs_type] = pd.read_parquet(filepath, engine='fastparquet')
        loaded_files.append(filepath.name)
        print(f"📂 Loaded {rs_type} RS from {filepath.name}")

    print(f"✅ Successfully loaded {len(rs_scores)} RS time series from {data_dir}")
    print(f"📄 Files: {loaded_files}")

    return rs_scores

def create_fake_data(
    n_tickers: int = 20,
    n_days: int = 60,
    n_themes: int = 4,
    n_subthemes_per_theme: int = 2
) -> tuple[Dict[str, pd.DataFrame], pd.DataFrame]:
    """
    Create fake data for testing and examples.
    
    Args:
        n_tickers: Number of tickers to create
        n_days: Number of days of data
        n_themes: Number of themes
        n_subthemes_per_theme: Number of subthemes per theme
        
    Returns:
        Tuple of (returns_dict, metadata_df)
    """
    import numpy as np
    
    # Set seed for reproducible results
    np.random.seed(42)
    
    # Create date range
    dates = pd.date_range('2024-01-01', periods=n_days, freq='D')
    
    # Create ticker names
    tickers = [f'TICK_{i:02d}' for i in range(n_tickers)]
    
    # Create themes and subthemes
    themes = [f'Theme_{i}' for i in range(n_themes)]
    subthemes = []
    for theme in themes:
        for j in range(n_subthemes_per_theme):
            subthemes.append(f'{theme}_Sub_{j}')
    
    # Assign tickers to themes/subthemes
    metadata = pd.DataFrame(index=tickers)
    metadata['Theme'] = np.random.choice(themes, n_tickers)
    metadata['Subtheme'] = [
        np.random.choice([s for s in subthemes if s.startswith(theme)])
        for theme in metadata['Theme']
    ]
    
    # Create returns data with some structure
    returns_data = {}
    
    # Create base performance levels for each ticker
    ticker_quality = np.random.normal(0, 0.02, n_tickers)  # Base performance
    
    for horizon in ['return_1d', 'return_20d', 'return_60d']:
        # Different volatility for different horizons
        if horizon == 'return_1d':
            vol = 0.02
        elif horizon == 'return_20d':
            vol = 0.05
        else:  # return_60d
            vol = 0.08
        
        # Create returns matrix
        returns_matrix = np.zeros((n_days, n_tickers))
        
        for i, ticker in enumerate(tickers):
            # Base return level for this ticker
            base_return = ticker_quality[i]
            
            # Add some trend and noise
            trend = np.linspace(0, ticker_quality[i] * 0.5, n_days)
            noise = np.random.normal(0, vol, n_days)
            
            returns_matrix[:, i] = base_return + trend + noise
        
        returns_data[horizon] = pd.DataFrame(
            returns_matrix, 
            index=dates, 
            columns=tickers
        )
    
    print(f"📊 Created fake data:")
    print(f"   Tickers: {n_tickers}")
    print(f"   Days: {n_days}")
    print(f"   Themes: {n_themes}")
    print(f"   Subthemes: {len(subthemes)}")
    print(f"   Return horizons: {list(returns_data.keys())}")
    
    return returns_data, metadata

def validate_rs_scores(rs_scores: Dict[str, pd.DataFrame]) -> bool:
    """
    Validate that RS scores are computed correctly.
    
    Args:
        rs_scores: Dict with RS DataFrames
        
    Returns:
        True if validation passes
    """
    print("🔍 Validating RS scores...")
    
    all_valid = True
    
    for rs_type, rs_df in rs_scores.items():
        if rs_type.endswith('_scores') or rs_type == 'weighted_returns':
            continue
            
        # Check value range
        min_val = rs_df.min().min()
        max_val = rs_df.max().max()
        
        if not (0 <= min_val <= max_val <= 1):
            print(f"❌ {rs_type} values out of range: [{min_val:.4f}, {max_val:.4f}]")
            all_valid = False
        else:
            print(f"✅ {rs_type} values in range: [{min_val:.4f}, {max_val:.4f}]")
        
        # Check for NaN values
        nan_count = rs_df.isna().sum().sum()
        if nan_count > 0:
            print(f"⚠️  {rs_type} has {nan_count} NaN values")
        else:
            print(f"✅ {rs_type} has no NaN values")
    
    return all_valid
