"""
Utility functions for relative strength module.
"""

import pandas as pd
from pathlib import Path
from typing import Dict, Optional
from datetime import datetime

def save_rs_data(
    rs_scores: Dict[str, pd.DataFrame], 
    output_dir: Path,
    timestamp: Optional[str] = None
) -> Dict[str, Path]:
    """
    Save RS time series to parquet files.
    
    Args:
        rs_scores: Dict with 'global', 'theme', 'subtheme' DataFrames
        output_dir: Directory to save files
        timestamp: Optional timestamp string, defaults to current time
        
    Returns:
        Dict mapping RS type to file path
    """
    if timestamp is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    saved_files = {}
    for rs_type, rs_df in rs_scores.items():
        if rs_type.endswith('_scores') or rs_type == 'weighted_returns':  # Skip intermediate data
            continue
            
        filename = f'rs_{rs_type}_timeseries_{timestamp}.parquet'
        filepath = output_dir / filename
        rs_df.to_parquet(filepath, engine='fastparquet')
        saved_files[rs_type] = filepath
        print(f"💾 Saved {rs_type} RS to {filepath}")
    
    return saved_files

def load_rs_data(data_dir: Path, timestamp: Optional[str] = None) -> Dict[str, pd.DataFrame]:
    """
    Load RS time series from parquet files.
    
    Args:
        data_dir: Directory containing RS files
        timestamp: Optional specific timestamp, defaults to latest
        
    Returns:
        Dict with 'global', 'theme', 'subtheme' DataFrames
    """
    if not data_dir.exists():
        raise FileNotFoundError(f"Data directory not found: {data_dir}")
    
    rs_scores = {}
    
    for rs_type in ['global', 'theme', 'subtheme']:
        if timestamp:
            # Load specific timestamp
            filename = f'rs_{rs_type}_timeseries_{timestamp}.parquet'
            filepath = data_dir / filename
        else:
            # Find latest file
            pattern = f'rs_{rs_type}_timeseries_*.parquet'
            files = list(data_dir.glob(pattern))
            if not files:
                raise FileNotFoundError(f"No {rs_type} RS files found in {data_dir}")
            filepath = max(files, key=lambda p: p.stat().st_mtime)
        
        if not filepath.exists():
            raise FileNotFoundError(f"RS file not found: {filepath}")
        
        rs_scores[rs_type] = pd.read_parquet(filepath, engine='fastparquet')
        print(f"📂 Loaded {rs_type} RS from {filepath}")
    
    return rs_scores

def create_fake_data(
    n_tickers: int = 20,
    n_days: int = 60,
    n_themes: int = 4,
    n_subthemes_per_theme: int = 2
) -> tuple[Dict[str, pd.DataFrame], pd.DataFrame]:
    """
    Create fake data for testing and examples.
    
    Args:
        n_tickers: Number of tickers to create
        n_days: Number of days of data
        n_themes: Number of themes
        n_subthemes_per_theme: Number of subthemes per theme
        
    Returns:
        Tuple of (returns_dict, metadata_df)
    """
    import numpy as np
    
    # Set seed for reproducible results
    np.random.seed(42)
    
    # Create date range
    dates = pd.date_range('2024-01-01', periods=n_days, freq='D')
    
    # Create ticker names
    tickers = [f'TICK_{i:02d}' for i in range(n_tickers)]
    
    # Create themes and subthemes
    themes = [f'Theme_{i}' for i in range(n_themes)]
    subthemes = []
    for theme in themes:
        for j in range(n_subthemes_per_theme):
            subthemes.append(f'{theme}_Sub_{j}')
    
    # Assign tickers to themes/subthemes
    metadata = pd.DataFrame(index=tickers)
    metadata['Theme'] = np.random.choice(themes, n_tickers)
    metadata['Subtheme'] = [
        np.random.choice([s for s in subthemes if s.startswith(theme)])
        for theme in metadata['Theme']
    ]
    
    # Create returns data with some structure
    returns_data = {}
    
    # Create base performance levels for each ticker
    ticker_quality = np.random.normal(0, 0.02, n_tickers)  # Base performance
    
    for horizon in ['return_1d', 'return_20d', 'return_60d']:
        # Different volatility for different horizons
        if horizon == 'return_1d':
            vol = 0.02
        elif horizon == 'return_20d':
            vol = 0.05
        else:  # return_60d
            vol = 0.08
        
        # Create returns matrix
        returns_matrix = np.zeros((n_days, n_tickers))
        
        for i, ticker in enumerate(tickers):
            # Base return level for this ticker
            base_return = ticker_quality[i]
            
            # Add some trend and noise
            trend = np.linspace(0, ticker_quality[i] * 0.5, n_days)
            noise = np.random.normal(0, vol, n_days)
            
            returns_matrix[:, i] = base_return + trend + noise
        
        returns_data[horizon] = pd.DataFrame(
            returns_matrix, 
            index=dates, 
            columns=tickers
        )
    
    print(f"📊 Created fake data:")
    print(f"   Tickers: {n_tickers}")
    print(f"   Days: {n_days}")
    print(f"   Themes: {n_themes}")
    print(f"   Subthemes: {len(subthemes)}")
    print(f"   Return horizons: {list(returns_data.keys())}")
    
    return returns_data, metadata

def validate_rs_scores(rs_scores: Dict[str, pd.DataFrame]) -> bool:
    """
    Validate that RS scores are computed correctly.
    
    Args:
        rs_scores: Dict with RS DataFrames
        
    Returns:
        True if validation passes
    """
    print("🔍 Validating RS scores...")
    
    all_valid = True
    
    for rs_type, rs_df in rs_scores.items():
        if rs_type.endswith('_ranks'):
            continue
            
        # Check value range
        min_val = rs_df.min().min()
        max_val = rs_df.max().max()
        
        if not (0 <= min_val <= max_val <= 1):
            print(f"❌ {rs_type} values out of range: [{min_val:.4f}, {max_val:.4f}]")
            all_valid = False
        else:
            print(f"✅ {rs_type} values in range: [{min_val:.4f}, {max_val:.4f}]")
        
        # Check for NaN values
        nan_count = rs_df.isna().sum().sum()
        if nan_count > 0:
            print(f"⚠️  {rs_type} has {nan_count} NaN values")
        else:
            print(f"✅ {rs_type} has no NaN values")
    
    return all_valid
