#!/usr/bin/env python3
"""
Validate the CORRECT relative strength approach:

1. Compute weighted returns for each ticker (combining horizons)
2. Rank these weighted returns within respective groupings per day

This demonstrates the proper two-step process.
"""

import pandas as pd
import numpy as np
from calculator import RelativeStrengthCalculator

def demonstrate_correct_rs_calculation():
    """Demonstrate the correct RS calculation approach step by step."""
    
    print("🎯 CORRECT RELATIVE STRENGTH CALCULATION")
    print("=" * 60)
    print("Step 1: Compute weighted returns")
    print("Step 2: Rank weighted returns within groupings per day")
    print()
    
    # Create simple test data
    print("📊 Creating test data...")
    
    # 6 tickers, 3 days, 2 themes
    dates = pd.date_range('2024-01-01', periods=3, freq='D')
    tickers = ['A1', 'A2', 'A3', 'B1', 'B2', 'B3']
    
    # Metadata: A tickers in Theme_X, B tickers in Theme_Y
    metadata = pd.DataFrame({
        'Theme': ['Theme_X', 'Theme_X', 'Theme_X', 'Theme_Y', 'Theme_Y', 'Theme_Y'],
        'Subtheme': ['X_Sub1', 'X_Sub1', 'X_Sub2', 'Y_Sub1', 'Y_Sub2', 'Y_Sub2']
    }, index=tickers)
    
    print("📋 Metadata:")
    print(metadata)
    
    # Create returns data with clear patterns
    # A1 > A2 > A3 within Theme_X
    # B1 > B2 > B3 within Theme_Y
    # Overall: A1 > B1 > A2 > B2 > A3 > B3
    
    np.random.seed(42)
    
    returns_data = {}
    for horizon in ['return_5d', 'return_20d']:
        if horizon == 'return_5d':
            # Short-term returns
            base_returns = {
                'A1': 0.05, 'A2': 0.03, 'A3': 0.01,  # Theme_X
                'B1': 0.04, 'B2': 0.02, 'B3': 0.00   # Theme_Y
            }
        else:  # return_20d
            # Long-term returns
            base_returns = {
                'A1': 0.08, 'A2': 0.06, 'A3': 0.04,  # Theme_X
                'B1': 0.07, 'B2': 0.05, 'B3': 0.03   # Theme_Y
            }
        
        # Create returns matrix with small noise
        returns_matrix = np.zeros((3, 6))
        for i, ticker in enumerate(tickers):
            base = base_returns[ticker]
            noise = np.random.normal(0, 0.001, 3)  # Very small noise
            returns_matrix[:, i] = base + noise
        
        returns_data[horizon] = pd.DataFrame(
            returns_matrix, index=dates, columns=tickers
        )
    
    print(f"\n📈 Returns data:")
    for horizon, df in returns_data.items():
        print(f"\n{horizon}:")
        print(df.round(4))
    
    # Initialize RS calculator
    print(f"\n⚙️  Initializing RS Calculator...")
    rs_calc = RelativeStrengthCalculator(
        horizons=['return_5d', 'return_20d'],
        weights={'return_5d': 0.3, 'return_20d': 0.7}  # More weight on long-term
    )
    
    # STEP 1: Compute weighted returns
    print(f"\n📊 STEP 1: Computing weighted returns...")
    weighted_returns = rs_calc.compute_weighted_returns(returns_data)
    print("Weighted returns (0.3 * return_5d + 0.7 * return_20d):")
    print(weighted_returns.round(4))
    
    # Verify weighted calculation manually for one ticker
    print(f"\n🔍 Manual verification for A1:")
    a1_5d = returns_data['return_5d'].loc['2024-01-01', 'A1']
    a1_20d = returns_data['return_20d'].loc['2024-01-01', 'A1']
    a1_weighted_manual = 0.3 * a1_5d + 0.7 * a1_20d
    a1_weighted_calc = weighted_returns.loc['2024-01-01', 'A1']
    print(f"   Manual: 0.3 * {a1_5d:.4f} + 0.7 * {a1_20d:.4f} = {a1_weighted_manual:.4f}")
    print(f"   Calculated: {a1_weighted_calc:.4f}")
    print(f"   Match: {'✅' if abs(a1_weighted_manual - a1_weighted_calc) < 1e-10 else '❌'}")
    
    # STEP 2: Compute RS scores
    print(f"\n🎯 STEP 2: Computing RS scores...")
    rs_scores = rs_calc.compute_all_rs_scores(returns_data, metadata)
    
    # Show results for first date
    date = dates[0]
    print(f"\n📊 RS SCORES FOR {date.strftime('%Y-%m-%d')}:")
    print("=" * 50)
    
    # Global RS
    print(f"\n🌍 GLOBAL RS (vs ALL 6 tickers):")
    global_rs = rs_scores['global'].loc[date].sort_values(ascending=False)
    weighted_ret = weighted_returns.loc[date].sort_values(ascending=False)
    
    print(f"   {'Ticker':<6} {'Weighted Ret':<12} {'Global RS':<10} {'Rank':<4}")
    print(f"   {'-'*6} {'-'*12} {'-'*10} {'-'*4}")
    for i, (ticker, rs_score) in enumerate(global_rs.items()):
        ret = weighted_ret[ticker]
        rank = i + 1
        print(f"   {ticker:<6} {ret:<12.4f} {rs_score:<10.4f} {rank:<4}")
    
    # Theme RS
    print(f"\n🎯 THEME RS (vs SAME THEME tickers):")
    theme_rs = rs_scores['theme'].loc[date]
    
    for theme in ['Theme_X', 'Theme_Y']:
        theme_tickers = metadata[metadata['Theme'] == theme].index
        print(f"\n   {theme} ({list(theme_tickers)}):")
        print(f"   {'Ticker':<6} {'Weighted Ret':<12} {'Theme RS':<10} {'Theme Rank':<10}")
        print(f"   {'-'*6} {'-'*12} {'-'*10} {'-'*10}")
        
        # Sort by weighted returns within theme
        theme_weighted_ret = weighted_returns.loc[date, theme_tickers].sort_values(ascending=False)
        for i, (ticker, ret) in enumerate(theme_weighted_ret.items()):
            rs_score = theme_rs[ticker]
            rank = i + 1
            print(f"   {ticker:<6} {ret:<12.4f} {rs_score:<10.4f} {rank:<10}")
    
    # Validation checks
    print(f"\n✅ VALIDATION CHECKS:")
    print("=" * 30)
    
    # Check 1: Higher weighted returns should lead to higher RS scores
    print("Check 1: Higher weighted returns → Higher RS scores")
    for date in dates:
        weighted_ret_day = weighted_returns.loc[date]
        global_rs_day = rs_scores['global'].loc[date]
        correlation = weighted_ret_day.corr(global_rs_day)
        print(f"   {date.strftime('%Y-%m-%d')}: correlation = {correlation:.6f} {'✅' if correlation > 0.99 else '❌'}")
    
    # Check 2: RS scores in 0-1 range
    print(f"\nCheck 2: RS scores in 0-1 range")
    for rs_type in ['global', 'theme', 'subtheme']:
        rs_data = rs_scores[rs_type]
        min_val = rs_data.min().min()
        max_val = rs_data.max().max()
        in_range = 0 <= min_val <= max_val <= 1
        print(f"   {rs_type}: [{min_val:.4f}, {max_val:.4f}] {'✅' if in_range else '❌'}")
    
    # Check 3: Theme rankings are correct
    print(f"\nCheck 3: Theme rankings within groups")
    date = dates[0]
    
    # Within Theme_X: A1 > A2 > A3
    theme_x_tickers = ['A1', 'A2', 'A3']
    theme_x_rs = rs_scores['theme'].loc[date, theme_x_tickers]
    theme_x_correct = (theme_x_rs['A1'] > theme_x_rs['A2'] > theme_x_rs['A3'])
    print(f"   Theme_X (A1 > A2 > A3): {theme_x_rs.round(4).to_dict()} {'✅' if theme_x_correct else '❌'}")
    
    # Within Theme_Y: B1 > B2 > B3
    theme_y_tickers = ['B1', 'B2', 'B3']
    theme_y_rs = rs_scores['theme'].loc[date, theme_y_tickers]
    theme_y_correct = (theme_y_rs['B1'] > theme_y_rs['B2'] > theme_y_rs['B3'])
    print(f"   Theme_Y (B1 > B2 > B3): {theme_y_rs.round(4).to_dict()} {'✅' if theme_y_correct else '❌'}")
    
    print(f"\n🎉 CORRECT RS CALCULATION DEMONSTRATED!")
    print("Key points:")
    print("   ✅ Step 1: Compute weighted returns (combine horizons)")
    print("   ✅ Step 2: Rank weighted returns within groupings")
    print("   ✅ Higher weighted returns → Higher RS scores")
    print("   ✅ Rankings are correct within themes")
    
    return rs_scores, weighted_returns, metadata

if __name__ == "__main__":
    try:
        rs_scores, weighted_returns, metadata = demonstrate_correct_rs_calculation()
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")
        raise
