"""
Relative Strength Module

This module provides comprehensive relative strength calculation and analysis tools.

For each ticker, computes three time series:
1. Global RS: ticker vs ALL tickers (0-1, higher=better)
2. Theme RS: ticker vs SAME theme tickers (0-1, higher=better)  
3. Subtheme RS: ticker vs SAME subtheme tickers (0-1, higher=better)
"""

from .calculator import RelativeStrengthCalculator
from .analyzer import RSAnalyzer
from .utils import load_rs_data, save_rs_data, list_available_rs_dates, smart_rs_date_selection

__all__ = [
    'RelativeStrengthCalculator',
    'RSAnalyzer',
    'load_rs_data',
    'save_rs_data',
    'list_available_rs_dates',
    'smart_rs_date_selection'
]

__version__ = '1.0.0'
