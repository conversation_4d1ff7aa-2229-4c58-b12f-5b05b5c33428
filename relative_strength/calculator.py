"""
Core Relative Strength Calculator

Computes three time series per ticker:
1. Global RS: vs all tickers
2. Theme RS: vs same theme tickers
3. Subtheme RS: vs same subtheme tickers
"""

import pandas as pd
import numpy as np
from typing import Dict, List
from datetime import datetime

class RelativeStrengthCalculator:
    """
    Calculate relative strength scores at multiple levels.
    
    All RS scores are percentile scores (0-1) where:
    - 1.0 = best performer in group
    - 0.5 = median performer in group  
    - 0.0 = worst performer in group
    """
    
    def __init__(
        self,
        horizons: List[str] = None,
        weights: Dict[str, float] = None
    ):
        """
        Initialize RS calculator.
        
        Args:
            horizons: Return horizons to use (e.g., ['return_20d', 'return_60d'])
            weights: Weights for each horizon (must sum to 1.0)
        """
        self.horizons = horizons or ['return_20d', 'return_60d', 'return_200d']
        
        # Default equal weights
        if weights is None:
            weights = {h: 1.0/len(self.horizons) for h in self.horizons}
        
        self.weights = weights
        self._validate_weights()
        
    def _validate_weights(self):
        """Validate that weights sum to 1.0 and match horizons."""
        if set(self.weights.keys()) != set(self.horizons):
            raise ValueError("Weight keys must match horizons exactly")
        
        weight_sum = sum(self.weights.values())
        if not np.isclose(weight_sum, 1.0, atol=1e-6):
            raise ValueError(f"Weights must sum to 1.0, got {weight_sum}")
    
    def compute_weighted_returns(self, returns: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Compute weighted returns by combining multiple horizons.

        Args:
            returns: Dict of {horizon: DataFrame} with returns data

        Returns:
            DataFrame with weighted returns (dates x tickers)
        """
        print("⚖️  Computing weighted returns...")

        # Weighted combination of returns
        weighted_returns = sum(
            returns[h] * self.weights[h]
            for h in self.horizons
        )

        weighted_returns.index.name = 'date'
        print(f"   Weighted returns shape: {weighted_returns.shape}")
        return weighted_returns

    def compute_global_rs(self, returns: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Compute global RS scores: each ticker vs ALL tickers.

        Steps:
        1. Compute weighted returns for each ticker
        2. Score weighted returns across ALL tickers per day (pct=True: higher=better)

        Args:
            returns: Dict of {horizon: DataFrame} with returns data

        Returns:
            DataFrame with global RS time series (dates x tickers)
        """
        print("🌍 Computing global RS scores...")

        # Step 1: Compute weighted returns
        weighted_returns = self.compute_weighted_returns(returns)

        # Step 2: Score weighted returns across ALL tickers per day (pct=True: higher=better)
        global_rs = weighted_returns.rank(axis=1, pct=True).fillna(0)

        global_rs.index.name = 'date'
        print(f"   Global RS shape: {global_rs.shape}")
        return global_rs
    
    def compute_theme_rs(
        self,
        returns: Dict[str, pd.DataFrame],
        metadata: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Compute theme RS scores: each ticker vs SAME THEME tickers.

        Steps:
        1. Compute weighted returns for each ticker
        2. Score weighted returns within SAME THEME per day (pct=True: higher=better)

        Args:
            returns: Dict of {horizon: DataFrame} with returns data
            metadata: DataFrame with Theme and Subtheme columns

        Returns:
            DataFrame with theme RS time series (dates x tickers)
        """
        print("🎯 Computing theme RS scores...")

        # Step 1: Compute weighted returns
        weighted_returns = self.compute_weighted_returns(returns)

        # Step 2: Score weighted returns within same theme per day
        theme_rs = self._score_within_groups(weighted_returns, metadata, 'Theme')

        theme_rs.index.name = 'date'
        print(f"   Theme RS shape: {theme_rs.shape}")
        return theme_rs
    
    def compute_subtheme_rs(
        self,
        returns: Dict[str, pd.DataFrame],
        metadata: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Compute subtheme RS scores: each ticker vs SAME SUBTHEME tickers.

        Steps:
        1. Compute weighted returns for each ticker
        2. Score weighted returns within SAME SUBTHEME per day (pct=True: higher=better)

        Args:
            returns: Dict of {horizon: DataFrame} with returns data
            metadata: DataFrame with Theme and Subtheme columns

        Returns:
            DataFrame with subtheme RS time series (dates x tickers)
        """
        print("🎪 Computing subtheme RS scores...")

        # Step 1: Compute weighted returns (already computed, reuse)
        weighted_returns = self.compute_weighted_returns(returns)

        # Step 2: Score weighted returns within same subtheme per day
        subtheme_rs = self._score_within_groups(weighted_returns, metadata, 'Subtheme')

        subtheme_rs.index.name = 'date'
        print(f"   Subtheme RS shape: {subtheme_rs.shape}")
        return subtheme_rs
    
    def _score_within_groups(
        self,
        weighted_returns: pd.DataFrame,
        metadata: pd.DataFrame,
        group_col: str
    ) -> pd.DataFrame:
        """
        Score weighted returns within each group (theme or subtheme) per day.

        Uses percentile ranking where higher weighted returns get higher scores.

        Args:
            weighted_returns: Weighted returns DataFrame (dates x tickers)
            metadata: Metadata with grouping column
            group_col: Column name to group by ('Theme' or 'Subtheme')

        Returns:
            DataFrame with within-group percentile scores (0-1, higher=better)
        """
        result_df = pd.DataFrame(
            index=weighted_returns.index,
            columns=weighted_returns.columns,
            dtype=float
        )

        # Process each date
        for date in weighted_returns.index:
            date_returns = weighted_returns.loc[date].dropna()

            # Process each group
            for group_name in metadata[group_col].unique():
                group_tickers = metadata[metadata[group_col] == group_name].index
                available_tickers = [t for t in group_tickers if t in date_returns.index]

                if len(available_tickers) > 1:
                    # Multiple tickers: score weighted returns within group using pct=True
                    group_weighted_returns = date_returns[available_tickers]
                    group_scores = group_weighted_returns.rank(pct=True)  # Higher return = higher score
                    result_df.loc[date, available_tickers] = group_scores
                elif len(available_tickers) == 1:
                    # Single ticker: median score
                    result_df.loc[date, available_tickers[0]] = 0.5

        return result_df.fillna(0)
    
    def compute_all_rs_scores(
        self,
        returns: Dict[str, pd.DataFrame],
        metadata: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """
        Compute all three RS score types efficiently.

        Steps:
        1. Compute weighted returns once
        2. Score within different groupings (global, theme, subtheme) using pct=True

        Args:
            returns: Dict of {horizon: DataFrame} with returns data
            metadata: DataFrame with Theme and Subtheme columns

        Returns:
            Dict with keys: 'global', 'theme', 'subtheme'
        """
        print("\n" + "="*50)
        print("🚀 COMPUTING RELATIVE STRENGTH SCORES")
        print("="*50)

        # Validate inputs
        self._validate_inputs(returns, metadata)

        # Step 1: Compute weighted returns once
        print("⚖️  Computing weighted returns...")
        weighted_returns = self.compute_weighted_returns(returns)

        # Step 2: Rank within different groupings
        print("🌍 Scoring globally...")
        global_rs = weighted_returns.rank(axis=1, pct=True).fillna(0)
        global_rs.index.name = 'date'

        print("🎯 Scoring within themes...")
        theme_rs = self._score_within_groups(weighted_returns, metadata, 'Theme')

        print("🎪 Scoring within subthemes...")
        subtheme_rs = self._score_within_groups(weighted_returns, metadata, 'Subtheme')

        print(f"\n✅ All RS scores computed successfully!")
        print(f"   Global RS: {global_rs.shape}")
        print(f"   Theme RS: {theme_rs.shape}")
        print(f"   Subtheme RS: {subtheme_rs.shape}")

        return {
            'global': global_rs,
            'theme': theme_rs,
            'subtheme': subtheme_rs,
            'weighted_returns': weighted_returns  # Include for debugging
        }
    
    def _validate_inputs(self, returns: Dict[str, pd.DataFrame], metadata: pd.DataFrame):
        """Validate input data."""
        # Check horizons exist in returns
        missing_horizons = set(self.horizons) - set(returns.keys())
        if missing_horizons:
            raise ValueError(f"Missing horizons in returns: {missing_horizons}")
        
        # Check metadata has required columns
        required_cols = ['Theme', 'Subtheme']
        missing_cols = set(required_cols) - set(metadata.columns)
        if missing_cols:
            raise ValueError(f"Missing columns in metadata: {missing_cols}")
        
        # Check ticker alignment
        first_horizon = self.horizons[0]
        returns_tickers = set(returns[first_horizon].columns)
        metadata_tickers = set(metadata.index)
        
        if not returns_tickers.issubset(metadata_tickers):
            missing_meta = returns_tickers - metadata_tickers
            print(f"⚠️  Warning: {len(missing_meta)} tickers missing metadata: {list(missing_meta)[:5]}...")
        
        print(f"📊 Input validation passed:")
        print(f"   Horizons: {self.horizons}")
        print(f"   Weights: {self.weights}")
        print(f"   Returns shape: {returns[first_horizon].shape}")
        print(f"   Metadata shape: {metadata.shape}")
        print(f"   Themes: {metadata['Theme'].nunique()}")
        print(f"   Subthemes: {metadata['Subtheme'].nunique()}")
