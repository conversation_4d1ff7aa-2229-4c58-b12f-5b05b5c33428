"""
Core Relative Strength Calculator

Computes three time series per ticker:
1. Global RS: vs all tickers
2. Theme RS: vs same theme tickers
3. Subtheme RS: vs same subtheme tickers
"""

import pandas as pd
import numpy as np
from typing import Dict, List
from datetime import datetime

class RelativeStrengthCalculator:
    """
    Calculate relative strength scores at multiple levels.
    
    All RS scores are percentile ranks (0-1) where:
    - 1.0 = best performer in group
    - 0.5 = median performer in group  
    - 0.0 = worst performer in group
    """
    
    def __init__(
        self,
        horizons: List[str] = None,
        weights: Dict[str, float] = None
    ):
        """
        Initialize RS calculator.
        
        Args:
            horizons: Return horizons to use (e.g., ['return_20d', 'return_60d'])
            weights: Weights for each horizon (must sum to 1.0)
        """
        self.horizons = horizons or ['return_20d', 'return_60d', 'return_200d']
        
        # Default equal weights
        if weights is None:
            weights = {h: 1.0/len(self.horizons) for h in self.horizons}
        
        self.weights = weights
        self._validate_weights()
        
    def _validate_weights(self):
        """Validate that weights sum to 1.0 and match horizons."""
        if set(self.weights.keys()) != set(self.horizons):
            raise ValueError("Weight keys must match horizons exactly")
        
        weight_sum = sum(self.weights.values())
        if not np.isclose(weight_sum, 1.0, atol=1e-6):
            raise ValueError(f"Weights must sum to 1.0, got {weight_sum}")
    
    def compute_global_rs(self, returns: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Compute global RS scores: each ticker vs ALL tickers.
        
        Args:
            returns: Dict of {horizon: DataFrame} with returns data
            
        Returns:
            DataFrame with global RS time series (dates x tickers)
        """
        print("🌍 Computing global RS scores...")
        
        # Compute percentile ranks for each horizon
        horizon_ranks = {}
        for horizon in self.horizons:
            returns_df = returns[horizon]
            # rank(pct=True) gives percentiles where higher return = higher rank
            horizon_ranks[horizon] = returns_df.rank(axis=1, pct=True).fillna(0)
        
        # Weighted combination
        global_rs = sum(
            horizon_ranks[h] * self.weights[h] 
            for h in self.horizons
        )
        
        global_rs.index.name = 'date'
        print(f"   Global RS shape: {global_rs.shape}")
        return global_rs
    
    def compute_theme_rs(
        self, 
        returns: Dict[str, pd.DataFrame], 
        metadata: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Compute theme RS scores: each ticker vs SAME THEME tickers.
        
        Args:
            returns: Dict of {horizon: DataFrame} with returns data
            metadata: DataFrame with Theme and Subtheme columns
            
        Returns:
            DataFrame with theme RS time series (dates x tickers)
        """
        print("🎯 Computing theme RS scores...")
        
        # Get first horizon to establish structure
        first_horizon = self.horizons[0]
        template_df = returns[first_horizon]
        
        # Initialize result
        theme_rs = pd.DataFrame(
            index=template_df.index, 
            columns=template_df.columns,
            dtype=float
        )
        
        # Compute theme ranks for each horizon
        horizon_theme_ranks = {}
        for horizon in self.horizons:
            horizon_theme_ranks[horizon] = self._compute_within_group_ranks(
                returns[horizon], metadata, 'Theme'
            )
        
        # Weighted combination
        theme_rs = sum(
            horizon_theme_ranks[h] * self.weights[h] 
            for h in self.horizons
        )
        
        theme_rs.index.name = 'date'
        print(f"   Theme RS shape: {theme_rs.shape}")
        return theme_rs
    
    def compute_subtheme_rs(
        self, 
        returns: Dict[str, pd.DataFrame], 
        metadata: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Compute subtheme RS scores: each ticker vs SAME SUBTHEME tickers.
        
        Args:
            returns: Dict of {horizon: DataFrame} with returns data
            metadata: DataFrame with Theme and Subtheme columns
            
        Returns:
            DataFrame with subtheme RS time series (dates x tickers)
        """
        print("🎪 Computing subtheme RS scores...")
        
        # Compute subtheme ranks for each horizon
        horizon_subtheme_ranks = {}
        for horizon in self.horizons:
            horizon_subtheme_ranks[horizon] = self._compute_within_group_ranks(
                returns[horizon], metadata, 'Subtheme'
            )
        
        # Weighted combination
        subtheme_rs = sum(
            horizon_subtheme_ranks[h] * self.weights[h] 
            for h in self.horizons
        )
        
        subtheme_rs.index.name = 'date'
        print(f"   Subtheme RS shape: {subtheme_rs.shape}")
        return subtheme_rs
    
    def _compute_within_group_ranks(
        self, 
        returns_df: pd.DataFrame, 
        metadata: pd.DataFrame, 
        group_col: str
    ) -> pd.DataFrame:
        """
        Compute percentile ranks within each group (theme or subtheme).
        
        Args:
            returns_df: Returns data for one horizon
            metadata: Metadata with grouping column
            group_col: Column name to group by ('Theme' or 'Subtheme')
            
        Returns:
            DataFrame with within-group percentile ranks
        """
        result_df = pd.DataFrame(
            index=returns_df.index, 
            columns=returns_df.columns,
            dtype=float
        )
        
        # Process each date
        for date in returns_df.index:
            date_returns = returns_df.loc[date].dropna()
            
            # Process each group
            for group_name in metadata[group_col].unique():
                group_tickers = metadata[metadata[group_col] == group_name].index
                available_tickers = [t for t in group_tickers if t in date_returns.index]
                
                if len(available_tickers) > 1:
                    # Multiple tickers: compute percentile ranks
                    group_returns = date_returns[available_tickers]
                    group_ranks = group_returns.rank(pct=True)
                    result_df.loc[date, available_tickers] = group_ranks
                elif len(available_tickers) == 1:
                    # Single ticker: median rank
                    result_df.loc[date, available_tickers[0]] = 0.5
        
        return result_df.fillna(0)
    
    def compute_all_rs_scores(
        self, 
        returns: Dict[str, pd.DataFrame], 
        metadata: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """
        Compute all three RS score types.
        
        Args:
            returns: Dict of {horizon: DataFrame} with returns data
            metadata: DataFrame with Theme and Subtheme columns
            
        Returns:
            Dict with keys: 'global', 'theme', 'subtheme'
        """
        print("\n" + "="*50)
        print("🚀 COMPUTING RELATIVE STRENGTH SCORES")
        print("="*50)
        
        # Validate inputs
        self._validate_inputs(returns, metadata)
        
        # Compute all RS types
        global_rs = self.compute_global_rs(returns)
        theme_rs = self.compute_theme_rs(returns, metadata)
        subtheme_rs = self.compute_subtheme_rs(returns, metadata)
        
        print(f"\n✅ All RS scores computed successfully!")
        
        return {
            'global': global_rs,
            'theme': theme_rs,
            'subtheme': subtheme_rs
        }
    
    def _validate_inputs(self, returns: Dict[str, pd.DataFrame], metadata: pd.DataFrame):
        """Validate input data."""
        # Check horizons exist in returns
        missing_horizons = set(self.horizons) - set(returns.keys())
        if missing_horizons:
            raise ValueError(f"Missing horizons in returns: {missing_horizons}")
        
        # Check metadata has required columns
        required_cols = ['Theme', 'Subtheme']
        missing_cols = set(required_cols) - set(metadata.columns)
        if missing_cols:
            raise ValueError(f"Missing columns in metadata: {missing_cols}")
        
        # Check ticker alignment
        first_horizon = self.horizons[0]
        returns_tickers = set(returns[first_horizon].columns)
        metadata_tickers = set(metadata.index)
        
        if not returns_tickers.issubset(metadata_tickers):
            missing_meta = returns_tickers - metadata_tickers
            print(f"⚠️  Warning: {len(missing_meta)} tickers missing metadata: {list(missing_meta)[:5]}...")
        
        print(f"📊 Input validation passed:")
        print(f"   Horizons: {self.horizons}")
        print(f"   Weights: {self.weights}")
        print(f"   Returns shape: {returns[first_horizon].shape}")
        print(f"   Metadata shape: {metadata.shape}")
        print(f"   Themes: {metadata['Theme'].nunique()}")
        print(f"   Subthemes: {metadata['Subtheme'].nunique()}")
