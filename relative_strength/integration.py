"""
Integration module for connecting RS module with existing codebase.
"""

import pandas as pd
from typing import Dict, Optional
from pathlib import Path
import sys
import os

# Add project root to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from project_settings.config import get_rs_dir

from .calculator import RelativeStrengthCalculator
from .analyzer import RSAnalyzer
from .utils import save_rs_data, load_rs_data, list_available_rs_dates

def compute_enhanced_rs_scores(
    returns: Dict[str, pd.DataFrame],
    metadata: pd.DataFrame,
    horizons: Optional[list] = None,
    weights: Optional[dict] = None,
    output_dir: Optional[Path] = None
) -> Dict[str, pd.DataFrame]:
    """
    Compute enhanced RS scores for integration with existing pipeline.
    
    Args:
        returns: Dict of {horizon: DataFrame} with returns data
        metadata: DataFrame with Theme and Subtheme columns
        horizons: RS horizons to use
        weights: Weights for each horizon
        output_dir: Optional directory to save results
        
    Returns:
        Dict with 'global', 'theme', 'subtheme' RS DataFrames
    """
    print("🎯 Computing enhanced RS scores...")
    
    # Initialize calculator
    rs_calc = RelativeStrengthCalculator(horizons=horizons, weights=weights)
    
    # Compute RS scores
    rs_scores = rs_calc.compute_all_rs_scores(returns, metadata)
    
    # Save to configured RS directory or specified output directory
    if output_dir:
        save_rs_data(rs_scores, output_dir)
    else:
        save_rs_data(rs_scores)  # Uses configured RS directory
    
    return rs_scores

def create_enhanced_snapshot(
    rs_scores: Dict[str, pd.DataFrame],
    metadata: pd.DataFrame,
    close_prices: Optional[pd.Series] = None,
    additional_data: Optional[pd.DataFrame] = None
) -> pd.DataFrame:
    """
    Create enhanced snapshot with RS scores for integration.
    
    Args:
        rs_scores: Dict with RS DataFrames
        metadata: DataFrame with Theme and Subtheme columns
        close_prices: Optional latest close prices
        additional_data: Optional additional data to include
        
    Returns:
        Enhanced snapshot DataFrame
    """
    print("📸 Creating enhanced snapshot...")
    
    # Initialize analyzer
    analyzer = RSAnalyzer(rs_scores, metadata)
    
    # Get base snapshot
    snapshot = analyzer.get_latest_snapshot()
    
    # Add close prices if provided
    if close_prices is not None:
        snapshot['close'] = close_prices
    
    # Add additional data if provided
    if additional_data is not None:
        snapshot = snapshot.join(additional_data, how='left')
    
    print(f"✅ Enhanced snapshot created with {len(snapshot)} tickers and {len(snapshot.columns)} columns")
    
    return snapshot

def analyze_rs_performance(
    rs_scores: Dict[str, pd.DataFrame],
    metadata: pd.DataFrame
) -> Dict[str, pd.DataFrame]:
    """
    Generate comprehensive RS analysis for reporting.
    
    Args:
        rs_scores: Dict with RS DataFrames
        metadata: DataFrame with Theme and Subtheme columns
        
    Returns:
        Dict with analysis results
    """
    print("📊 Analyzing RS performance...")
    
    analyzer = RSAnalyzer(rs_scores, metadata)
    
    analysis = {
        'top_global': analyzer.get_top_performers('global', 10),
        'top_theme': analyzer.get_top_performers('theme', 10),
        'top_subtheme': analyzer.get_top_performers('subtheme', 10),
        'theme_stats': analyzer.analyze_by_theme(),
        'divergences': analyzer.find_divergences(),
        'snapshot': analyzer.get_latest_snapshot()
    }
    
    print(f"✅ RS analysis completed")
    
    return analysis
