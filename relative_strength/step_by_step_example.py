#!/usr/bin/env python3
"""
Step-by-Step Relative Strength Example with Fake Data

This example demonstrates the complete RS workflow:
1. Create fake data
2. Compute RS scores
3. Analyze results
4. Save/load data
"""

import pandas as pd
import numpy as np
from pathlib import Path

# Import our RS module
from calculator import RelativeStrengthCalculator
from analyzer import RSAnalyzer
from utils import create_fake_data, save_rs_data, load_rs_data, validate_rs_scores

def step_by_step_example():
    """Complete step-by-step example with fake data."""
    
    print("🚀 STEP-BY-STEP RELATIVE STRENGTH EXAMPLE")
    print("=" * 60)
    
    # STEP 1: Create fake data
    print("\n📊 STEP 1: Creating fake data...")
    print("-" * 30)
    
    returns_data, metadata = create_fake_data(
        n_tickers=15,      # 15 stocks
        n_days=30,         # 30 days of data
        n_themes=3,        # 3 themes
        n_subthemes_per_theme=2  # 2 subthemes per theme
    )
    
    print(f"\n✅ Fake data created:")
    print(f"   Returns shape: {returns_data['return_20d'].shape}")
    print(f"   Metadata shape: {metadata.shape}")
    print(f"\n📋 Sample metadata:")
    print(metadata.head(8))
    
    print(f"\n📈 Sample returns (last 5 days, first 5 tickers):")
    sample_returns = returns_data['return_20d'].tail(5).iloc[:, :5]
    print(sample_returns.round(4))
    
    # STEP 2: Initialize RS Calculator
    print(f"\n⚙️  STEP 2: Initialize RS Calculator...")
    print("-" * 30)
    
    rs_calc = RelativeStrengthCalculator(
        horizons=['return_1d', 'return_20d', 'return_60d'],
        weights={'return_1d': 0.2, 'return_20d': 0.5, 'return_60d': 0.3}
    )
    
    print(f"✅ RS Calculator initialized with:")
    print(f"   Horizons: {rs_calc.horizons}")
    print(f"   Weights: {rs_calc.weights}")
    
    # STEP 3: Compute RS scores
    print(f"\n🎯 STEP 3: Compute RS scores...")
    print("-" * 30)
    
    rs_scores = rs_calc.compute_all_rs_scores(returns_data, metadata)
    
    print(f"\n✅ RS scores computed:")
    for rs_type, rs_df in rs_scores.items():
        if not rs_type.endswith('_ranks'):
            print(f"   {rs_type}: {rs_df.shape}")
    
    # STEP 4: Validate results
    print(f"\n🔍 STEP 4: Validate RS scores...")
    print("-" * 30)
    
    is_valid = validate_rs_scores(rs_scores)
    print(f"\n{'✅ Validation PASSED' if is_valid else '❌ Validation FAILED'}")
    
    # STEP 5: Analyze results
    print(f"\n📊 STEP 5: Analyze results...")
    print("-" * 30)
    
    analyzer = RSAnalyzer(rs_scores, metadata)
    
    # Get latest snapshot
    snapshot = analyzer.get_latest_snapshot()
    print(f"✅ Latest snapshot created with {len(snapshot)} tickers")
    
    # Show top performers
    print(f"\n🏆 TOP 5 GLOBAL PERFORMERS:")
    top_global = analyzer.get_top_performers('global', 5)
    print(top_global.round(4))
    
    print(f"\n🎯 TOP 5 THEME PERFORMERS:")
    top_theme = analyzer.get_top_performers('theme', 5)
    print(top_theme.round(4))
    
    # Theme analysis
    print(f"\n📈 THEME ANALYSIS:")
    theme_stats = analyzer.analyze_by_theme()
    print(theme_stats[['rs_global_mean', 'ticker_count', 'rs_global_std']].round(4))
    
    # Find divergences
    print(f"\n🔍 DIVERGENCE ANALYSIS:")
    divergences = analyzer.find_divergences(threshold=0.4)
    if not divergences.empty:
        print("Found divergent stocks (high theme RS, low global RS):")
        print(divergences[['Theme', 'rs_global', 'rs_theme', 'global_percentile', 'theme_percentile']].round(2))
    else:
        print("No significant divergences found")
    
    # STEP 6: Individual ticker analysis
    print(f"\n📈 STEP 6: Individual ticker analysis...")
    print("-" * 30)
    
    # Pick a sample ticker
    sample_ticker = snapshot.index[0]
    ticker_ts = analyzer.get_ticker_timeseries(sample_ticker)
    
    print(f"🔍 Time series for {sample_ticker}:")
    print(f"   Theme: {metadata.loc[sample_ticker, 'Theme']}")
    print(f"   Subtheme: {metadata.loc[sample_ticker, 'Subtheme']}")
    print(f"\n   Last 5 days:")
    print(f"   {'Date':<12} {'Global RS':<10} {'Theme RS':<10} {'Subtheme RS':<12}")
    print(f"   {'-'*12} {'-'*10} {'-'*10} {'-'*12}")
    
    for date in ticker_ts.tail(5).index:
        global_rs = ticker_ts.loc[date, 'rs_global']
        theme_rs = ticker_ts.loc[date, 'rs_theme']
        subtheme_rs = ticker_ts.loc[date, 'rs_subtheme']
        print(f"   {date.strftime('%Y-%m-%d'):<12} {global_rs:<10.4f} {theme_rs:<10.4f} {subtheme_rs:<12.4f}")
    
    # STEP 7: Compare multiple tickers
    print(f"\n🔄 STEP 7: Compare multiple tickers...")
    print("-" * 30)
    
    # Pick tickers from same theme
    theme_0_tickers = metadata[metadata['Theme'] == 'Theme_0'].index[:3].tolist()
    if len(theme_0_tickers) >= 2:
        comparison = analyzer.compare_tickers(theme_0_tickers)
        print(f"Comparing tickers in Theme_0:")
        print(comparison[['Theme', 'rs_global', 'rs_theme', 'rs_subtheme', 'global_percentile']].round(3))
    
    # STEP 8: Save data
    print(f"\n💾 STEP 8: Save RS data...")
    print("-" * 30)

    # Save to configured RS directory
    saved_files = save_rs_data(rs_scores)

    print(f"✅ Saved {len(saved_files)} RS files to configured RS directory")
    for rs_type, filepath in saved_files.items():
        print(f"   {rs_type}: {filepath}")

    # STEP 9: Load data back (demonstrating date-aware loading)
    print(f"\n📂 STEP 9: Load data back...")
    print("-" * 30)

    # First, show what happens without specifying a date (warning)
    print("🔍 Loading without date specification (shows warning):")
    loaded_rs_scores_warning = load_rs_data()  # Will show warning

    # Then, show proper date-specific loading
    print(f"\n🔍 Loading with explicit date specification:")
    from datetime import datetime
    today_date = datetime.now().strftime('%Y_%m_%d')
    loaded_rs_scores = load_rs_data(date=today_date)

    print(f"\n✅ Loaded RS data:")
    for rs_type, rs_df in loaded_rs_scores.items():
        print(f"   {rs_type}: {rs_df.shape}")

    # Verify data integrity
    data_matches = all(
        rs_scores[rs_type].equals(loaded_rs_scores[rs_type])
        for rs_type in ['global', 'theme', 'subtheme']
    )
    print(f"{'✅ Data integrity verified' if data_matches else '❌ Data mismatch detected'}")

    # STEP 9b: Show available dates
    print(f"\n📅 STEP 9b: Show available RS data dates...")
    print("-" * 30)
    from utils import list_available_rs_dates
    available_dates = list_available_rs_dates()
    
    # STEP 10: Generate summary report
    print(f"\n📋 STEP 10: Generate summary report...")
    print("-" * 30)
    
    report = analyzer.generate_summary_report()
    print(report)
    
    print(f"\n🎉 STEP-BY-STEP EXAMPLE COMPLETED!")
    print("=" * 60)
    print(f"📚 What we demonstrated:")
    print(f"   ✅ Created fake data with themes and subthemes")
    print(f"   ✅ Computed 3 RS time series per ticker")
    print(f"   ✅ Validated RS scores (0-1 range, higher=better)")
    print(f"   ✅ Analyzed top performers and themes")
    print(f"   ✅ Found divergent stocks")
    print(f"   ✅ Tracked individual ticker time series")
    print(f"   ✅ Compared tickers within same theme")
    print(f"   ✅ Saved and loaded RS data")
    print(f"   ✅ Generated comprehensive report")
    
    return rs_scores, metadata, analyzer

def demonstrate_key_concepts():
    """Demonstrate key RS concepts with simple examples."""
    
    print(f"\n🎓 KEY CONCEPTS DEMONSTRATION")
    print("=" * 50)
    
    # Create very simple data for clear demonstration
    print(f"\n📊 Creating simple example data...")
    
    # 6 tickers, 2 themes, 5 days
    dates = pd.date_range('2024-01-01', periods=5, freq='D')
    tickers = ['BEST', 'GOOD', 'AVG_A', 'AVG_B', 'POOR', 'WORST']
    
    # Create metadata
    metadata = pd.DataFrame({
        'Theme': ['Tech', 'Tech', 'Finance', 'Finance', 'Tech', 'Finance'],
        'Subtheme': ['AI', 'Cloud', 'Banking', 'Insurance', 'AI', 'Banking']
    }, index=tickers)
    
    print(f"📋 Simple metadata:")
    print(metadata)
    
    # Create returns where names indicate performance
    # BEST > GOOD > AVG_A ≈ AVG_B > POOR > WORST
    base_returns = {
        'BEST': 0.05,   # Best performer
        'GOOD': 0.03,   # Good performer  
        'AVG_A': 0.01,  # Average
        'AVG_B': 0.01,  # Average
        'POOR': -0.02,  # Poor performer
        'WORST': -0.05  # Worst performer
    }
    
    # Create returns data
    returns_data = {}
    np.random.seed(123)  # For consistent results
    
    for horizon in ['return_20d']:  # Single horizon for simplicity
        returns_matrix = np.zeros((5, 6))
        for i, ticker in enumerate(tickers):
            base = base_returns[ticker]
            noise = np.random.normal(0, 0.005, 5)
            returns_matrix[:, i] = base + noise
        
        returns_data[horizon] = pd.DataFrame(
            returns_matrix, index=dates, columns=tickers
        )
    
    print(f"\n📈 Simple returns (designed so BEST > GOOD > AVG > POOR > WORST):")
    print(returns_data['return_20d'].round(4))
    
    # Compute RS scores
    rs_calc = RelativeStrengthCalculator(
        horizons=['return_20d'],
        weights={'return_20d': 1.0}
    )
    
    rs_scores = rs_calc.compute_all_rs_scores(returns_data, metadata)
    
    # Show results
    print(f"\n🎯 RS SCORES EXPLANATION:")
    print("=" * 40)
    
    latest_date = rs_scores['global'].index[-1]
    
    print(f"\n🌍 GLOBAL RS (vs ALL 6 tickers):")
    global_rs = rs_scores['global'].loc[latest_date].sort_values(ascending=False)
    for ticker, score in global_rs.items():
        rank = int(score * 6)  # Convert to rank out of 6
        print(f"   {ticker:<5}: {score:.3f} = {score*100:5.1f}th percentile (rank {rank}/6)")
    
    print(f"\n🎯 THEME RS (vs SAME THEME tickers):")
    theme_rs = rs_scores['theme'].loc[latest_date]
    
    for theme in ['Tech', 'Finance']:
        theme_tickers = metadata[metadata['Theme'] == theme].index
        print(f"\n   {theme} theme ({list(theme_tickers)}):")
        for ticker in theme_tickers:
            score = theme_rs[ticker]
            n_in_theme = len(theme_tickers)
            rank = int(score * n_in_theme) if score < 1.0 else n_in_theme
            print(f"     {ticker:<5}: {score:.3f} = {score*100:5.1f}th percentile within {theme} (rank {rank}/{n_in_theme})")
    
    print(f"\n💡 KEY INSIGHTS:")
    print(f"   • BEST has highest global RS (best vs all 6 tickers)")
    print(f"   • BEST also has high theme RS (best among Tech tickers)")
    print(f"   • AVG_A vs AVG_B: similar global RS, different theme RS")
    print(f"   • Theme RS shows relative performance within sector")
    print(f"   • All scores 0-1 where 1=best, 0=worst in group")

if __name__ == "__main__":
    try:
        # Run the complete step-by-step example
        rs_scores, metadata, analyzer = step_by_step_example()
        
        # Demonstrate key concepts
        demonstrate_key_concepts()
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        raise
