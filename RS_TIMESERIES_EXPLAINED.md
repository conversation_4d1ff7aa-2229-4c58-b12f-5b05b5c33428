# RS Time Series Explained: Three Time Series Per Ticker

## Clear Overview

For **EACH ticker**, we compute **THREE separate time series**:

### 1. 🌍 Global RS Time Series
- **What**: Ticker's percentile rank vs **ALL tickers** in the universe
- **Range**: 0-1 (where 1 = best performer globally, 0 = worst performer globally)
- **Updates**: Daily (or whenever new data is available)
- **Use**: Identify overall market leaders and laggards

### 2. 🎯 Theme RS Time Series  
- **What**: Ticker's percentile rank vs **tickers in SAME theme** only
- **Range**: 0-1 (where 1 = best in theme, 0 = worst in theme)
- **Updates**: Daily (or whenever new data is available)
- **Use**: Identify leaders within each sector/theme

### 3. 🎪 Subtheme RS Time Series
- **What**: Ticker's percentile rank vs **tickers in SAME subtheme** only
- **Range**: 0-1 (where 1 = best in subtheme, 0 = worst in subtheme)
- **Updates**: Daily (or whenever new data is available)
- **Use**: Identify leaders among direct competitors

## Example: AAPL Stock

```
Date        Global RS  Theme RS   Subtheme RS
----------  ---------  ---------  -----------
2024-01-01    0.8500     0.7200      0.9100
2024-01-02    0.8200     0.6800      0.8900
2024-01-03    0.8700     0.7500      0.9300
...
```

**Interpretation for 2024-01-03:**
- Global RS: 0.8700 = 87th percentile vs ALL stocks (top 13%)
- Theme RS: 0.7500 = 75th percentile vs Tech stocks (top 25% in Tech)
- Subtheme RS: 0.9300 = 93rd percentile vs Consumer Electronics stocks (top 7% in niche)

## Key Principles

### ✅ Higher is Always Better
- **1.0** = Best performer in the group
- **0.5** = Median performer in the group  
- **0.0** = Worst performer in the group

### ✅ Consistent Calculation
All three RS scores use the same methodology:
1. Calculate returns over specified horizons (e.g., 20d, 22d, 200d)
2. Rank tickers by returns within their comparison group
3. Convert ranks to percentiles (0-1 scale)
4. Weight and combine multiple horizons

### ✅ Different Comparison Groups
- **Global**: Compare vs entire universe (e.g., 500 stocks)
- **Theme**: Compare vs theme peers (e.g., 50 AI stocks)
- **Subtheme**: Compare vs direct competitors (e.g., 8 Cloud Service stocks)

## Strategic Insights

### 🎯 Four Quadrant Analysis

| Global RS | Theme RS | Interpretation | Strategy |
|-----------|----------|----------------|----------|
| High | High | Strong stock in strong sector | **BUY** - Best of both worlds |
| High | Low | Strong stock in weak sector | **HOLD** - Stock-specific strength |
| Low | High | Weak stock in strong sector | **AVOID** - Sector play, pick better stock |
| Low | Low | Weak stock in weak sector | **SELL** - Avoid completely |

### 🔍 Divergence Opportunities

**Example Scenarios:**
- **High Theme RS + Low Global RS**: Strong within weak sector → Potential rotation play
- **High Subtheme RS + Low Theme RS**: Niche leader in struggling sector → Specialized opportunity
- **High Global RS + Low Theme RS**: Outperforming despite weak sector → Exceptional company

## Data Structure

### Time Series Files
```
data/processed/yyyy_mm_dd/
├── rs_global_timeseries_YYYYMMDD_HHMMSS.parquet     # Global RS for all tickers
├── rs_theme_timeseries_YYYYMMDD_HHMMSS.parquet      # Theme RS for all tickers  
└── rs_subtheme_timeseries_YYYYMMDD_HHMMSS.parquet   # Subtheme RS for all tickers
```

### DataFrame Structure
Each file contains a DataFrame with:
- **Index**: Dates
- **Columns**: Ticker symbols
- **Values**: RS scores (0-1, higher=better)

```python
# Example: Global RS time series
global_rs = pd.read_parquet('rs_global_timeseries_20241201_143022.parquet')
print(global_rs.head())

#             AAPL    MSFT    GOOGL   NVDA    ...
# 2024-01-01  0.85    0.72    0.68    0.91    ...
# 2024-01-02  0.82    0.75    0.71    0.89    ...
# 2024-01-03  0.87    0.78    0.69    0.92    ...
```

## Usage Examples

### Load and Analyze Single Ticker
```python
from rs_analysis_toolkit import RSAnalyzer

analyzer = RSAnalyzer()

# Get AAPL's three time series
aapl_global = analyzer.track_ticker_timeseries('AAPL', 'global')
aapl_theme = analyzer.track_ticker_timeseries('AAPL', 'theme')  
aapl_subtheme = analyzer.track_ticker_timeseries('AAPL', 'subtheme')
```

### Compare Tickers Within Theme
```python
# Get all AI theme tickers
ai_tickers = analyzer.snapshot[analyzer.snapshot['Theme'] == 'AI'].index

# Compare their theme RS scores
for ticker in ai_tickers:
    theme_rs = analyzer.snapshot.loc[ticker, 'rs_theme']
    print(f"{ticker}: {theme_rs:.4f}")
```

### Track RS Evolution
```python
# See how NVDA's RS scores evolved over time
nvda_evolution = analyzer.track_ticker_timeseries('NVDA', 'global')
print(f"NVDA RS trend: {nvda_evolution['rs_score'].diff().mean():+.6f} daily")
```

## Validation

Run the validation script to ensure everything works correctly:

```bash
python validate_rs_scores.py
```

This will:
- ✅ Test that RS scores are in 0-1 range
- ✅ Verify higher returns → higher RS scores
- ✅ Confirm theme/subtheme rankings work correctly
- ✅ Validate time series consistency

## Files to Run

1. **`validate_rs_scores.py`** - Validate the RS calculation logic
2. **`rs_timeseries_demo.py`** - See the three time series in action
3. **`rs_example.py`** - Comprehensive demonstration with real data
4. **`main.py`** - Run full pipeline with enhanced RS scores

## Key Takeaways

🎯 **Each ticker gets exactly 3 time series:**
- Global RS vs all tickers
- Theme RS vs same theme tickers  
- Subtheme RS vs same subtheme tickers

📈 **All scores use 0-1 scale where higher = better performance**

🔄 **Time series update with each new data point**

💡 **Multiple perspectives enable sophisticated analysis:**
- Market timing (global RS trends)
- Sector rotation (theme RS comparison)
- Stock picking (subtheme RS ranking)
