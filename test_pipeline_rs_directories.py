#!/usr/bin/env python3
"""
Test that the main pipeline uses the correct RS directory structure.
"""

import pandas as pd
from pathlib import Path
from project_settings.config import get_rs_dir, get_processed_dir

def test_pipeline_directories():
    """Test that pipeline uses correct directories."""
    print("🧪 TESTING PIPELINE DIRECTORY USAGE")
    print("=" * 50)
    
    # Create test CSV
    test_data = {
        'ticker': ['AAPL', 'MSFT', 'GOOGL'],
        'Theme': ['Tech', 'Tech', 'Tech'],
        'Subtheme': ['Hardware', 'Software', 'Search']
    }
    
    df = pd.DataFrame(test_data)
    df.to_csv('test_tickers_dir.csv', index=False)
    
    print(f"📝 Created test CSV with {len(df)} tickers")
    
    # Check directory structure before
    rs_dir = get_rs_dir()
    processed_dir = get_processed_dir()
    
    print(f"\n📁 Directory structure:")
    print(f"   RS directory: {rs_dir}")
    print(f"   Processed directory: {processed_dir}")
    print(f"   RS exists: {rs_dir.exists()}")
    print(f"   Processed exists: {processed_dir.exists()}")
    
    try:
        # Import and run main function
        from main import main
        
        print(f"\n🔄 Running main pipeline...")
        dashboard, output_path = main(
            input_csv='test_tickers_dir.csv',
            lookback=30,  # Shorter for testing
            close_horizons=['return_1d', 'return_5d'],
            rs_horizons=['return_5d'],
            weights={'return_5d': 1.0},
            sma_windows=[5]
        )
        
        print(f"\n✅ Pipeline completed successfully!")
        print(f"📊 Dashboard shape: {dashboard.shape}")
        print(f"📁 Output file: {output_path}")
        
        # Check if RS files were created in correct directory
        print(f"\n📂 Checking RS files in directory...")
        rs_files = list(rs_dir.glob("rs_*_timeseries_*.parquet"))
        
        if rs_files:
            print(f"✅ Found {len(rs_files)} RS files in {rs_dir}:")
            for file in sorted(rs_files):
                print(f"   📄 {file.name}")
        else:
            print(f"⚠️  No RS files found in {rs_dir}")
        
        # Check processed files
        processed_files = list(processed_dir.glob("*.parquet"))
        print(f"\n📂 Found {len(processed_files)} processed files in {processed_dir}:")
        for file in sorted(processed_files):
            print(f"   📄 {file.name}")
        
        # Verify RS columns in dashboard
        rs_columns = [col for col in dashboard.columns if col.startswith('rs_')]
        print(f"\n📈 RS columns in dashboard: {rs_columns}")
        
        if 'rs_global' in dashboard.columns:
            print(f"✅ Enhanced RS integration working")
        else:
            print(f"⚠️  Enhanced RS not found in dashboard")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test file
        if Path('test_tickers_dir.csv').exists():
            Path('test_tickers_dir.csv').unlink()
            print(f"🧹 Cleaned up test CSV")

if __name__ == "__main__":
    try:
        success = test_pipeline_directories()
        if success:
            print("\n🎉 Pipeline directory test completed successfully!")
            print("✅ RS files saved to data/relative_strength/")
            print("✅ Processed files saved to data/processed/")
            print("✅ Directory structure working correctly")
        else:
            print("\n❌ Pipeline directory test failed!")
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()
