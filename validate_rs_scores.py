#!/usr/bin/env python3
"""
Validation script to ensure RS scores are computed correctly.
Tests that higher returns lead to higher RS scores consistently.
"""

import pandas as pd
import numpy as np
from relative_strength_calculator import RelativeStrengthCalculator

def validate_rs_calculation():
    """Validate that RS calculation is working correctly."""
    print("🔍 VALIDATING RS SCORE CALCULATION")
    print("=" * 50)
    
    # Create simple test data
    print("📊 Creating test data...")
    
    # Simple test: 5 tickers, 10 days
    dates = pd.date_range('2024-01-01', periods=10, freq='D')
    tickers = ['TICK_A', 'TICK_B', 'TICK_C', 'TICK_D', 'TICK_E']
    
    # Create returns where TICK_A is always best, TICK_E is always worst
    np.random.seed(42)  # For reproducible results
    returns_data = {}
    
    for i, ticker in enumerate(tickers):
        # Create returns where higher index = better performance
        base_return = (i - 2) * 0.01  # -0.02, -0.01, 0, 0.01, 0.02
        noise = np.random.normal(0, 0.005, len(dates))
        returns_data[ticker] = base_return + noise
    
    returns_df = pd.DataFrame(returns_data, index=dates)
    
    print(f"✅ Test returns created:")
    print(f"   Shape: {returns_df.shape}")
    print(f"   Mean returns by ticker:")
    for ticker in tickers:
        print(f"     {ticker}: {returns_df[ticker].mean():+.4f}")
    
    # Create metadata
    meta = pd.DataFrame({
        'Theme': ['Tech', 'Tech', 'Finance', 'Finance', 'Finance'],
        'Subtheme': ['Software', 'Hardware', 'Banking', 'Banking', 'Insurance']
    }, index=tickers)
    
    print(f"\n📋 Test metadata:")
    print(meta)
    
    # Test RS calculation
    print(f"\n🎯 Testing RS calculation...")
    rs_calc = RelativeStrengthCalculator(
        rs_horizons=['return_1d'],  # Use 1-day for simplicity
        weights={'return_1d': 1.0}
    )
    
    # Prepare returns in expected format
    returns_dict = {'return_1d': returns_df}
    
    # Compute RS scores
    rs_scores = rs_calc.compute_all_rs_scores(returns_dict, meta)
    
    # Validation tests
    print(f"\n✅ VALIDATION TESTS")
    print("=" * 30)
    
    # Test 1: Check value ranges
    print("Test 1: Value ranges (should be 0-1)")
    for rs_type in ['global', 'theme', 'subtheme']:
        rs_data = rs_scores[rs_type]
        min_val = rs_data.min().min()
        max_val = rs_data.max().max()
        status = "✅" if 0 <= min_val <= max_val <= 1 else "❌"
        print(f"   {rs_type}: [{min_val:.4f}, {max_val:.4f}] {status}")
    
    # Test 2: Check that better returns → higher RS
    print(f"\nTest 2: Higher returns → Higher RS scores")
    latest_date = returns_df.index[-1]
    latest_returns = returns_df.loc[latest_date]
    latest_global_rs = rs_scores['global'].loc[latest_date]
    
    print(f"   Latest date: {latest_date}")
    print(f"   Returns vs Global RS:")
    for ticker in tickers:
        ret = latest_returns[ticker]
        rs = latest_global_rs[ticker]
        print(f"     {ticker}: return={ret:+.4f}, global_rs={rs:.4f}")
    
    # Check correlation
    correlation = latest_returns.corr(latest_global_rs)
    status = "✅" if correlation > 0.9 else "❌"
    print(f"   Correlation: {correlation:.4f} {status}")
    
    # Test 3: Check theme-relative rankings
    print(f"\nTest 3: Theme-relative rankings")
    tech_tickers = meta[meta['Theme'] == 'Tech'].index
    finance_tickers = meta[meta['Theme'] == 'Finance'].index
    
    print(f"   Tech theme ({list(tech_tickers)}):")
    tech_theme_rs = rs_scores['theme'].loc[latest_date, tech_tickers]
    for ticker in tech_tickers:
        print(f"     {ticker}: theme_rs={tech_theme_rs[ticker]:.4f}")
    
    print(f"   Finance theme ({list(finance_tickers)}):")
    finance_theme_rs = rs_scores['theme'].loc[latest_date, finance_tickers]
    for ticker in finance_tickers:
        print(f"     {ticker}: theme_rs={finance_theme_rs[ticker]:.4f}")
    
    # Test 4: Check that theme RS scores are relative within theme
    print(f"\nTest 4: Theme RS should be relative within theme")
    
    # Within Tech theme: TICK_A should have higher theme RS than TICK_B
    tech_a_theme_rs = rs_scores['theme'].loc[latest_date, 'TICK_A']
    tech_b_theme_rs = rs_scores['theme'].loc[latest_date, 'TICK_B']
    tech_test = tech_a_theme_rs > tech_b_theme_rs
    print(f"   Tech: TICK_A ({tech_a_theme_rs:.4f}) > TICK_B ({tech_b_theme_rs:.4f}): {'✅' if tech_test else '❌'}")
    
    # Within Finance theme: TICK_D should have higher theme RS than TICK_E
    finance_d_theme_rs = rs_scores['theme'].loc[latest_date, 'TICK_D']
    finance_e_theme_rs = rs_scores['theme'].loc[latest_date, 'TICK_E']
    finance_test = finance_d_theme_rs > finance_e_theme_rs
    print(f"   Finance: TICK_D ({finance_d_theme_rs:.4f}) > TICK_E ({finance_e_theme_rs:.4f}): {'✅' if finance_test else '❌'}")
    
    # Test 5: Time series consistency
    print(f"\nTest 5: Time series consistency")
    
    # Check that TICK_A (best performer) generally has high RS scores over time
    tick_a_global_ts = rs_scores['global']['TICK_A']
    tick_a_avg_rs = tick_a_global_ts.mean()
    tick_a_test = tick_a_avg_rs > 0.7  # Should be in top 30%
    print(f"   TICK_A average global RS: {tick_a_avg_rs:.4f} {'✅' if tick_a_test else '❌'}")
    
    # Check that TICK_E (worst performer) generally has low RS scores over time
    tick_e_global_ts = rs_scores['global']['TICK_E']
    tick_e_avg_rs = tick_e_global_ts.mean()
    tick_e_test = tick_e_avg_rs < 0.3  # Should be in bottom 30%
    print(f"   TICK_E average global RS: {tick_e_avg_rs:.4f} {'✅' if tick_e_test else '❌'}")
    
    # Overall validation
    all_tests = [
        0 <= min_val <= max_val <= 1,  # Range test
        correlation > 0.9,  # Correlation test
        tech_test,  # Tech theme test
        finance_test,  # Finance theme test
        tick_a_test,  # Best performer test
        tick_e_test  # Worst performer test
    ]
    
    print(f"\n🎯 OVERALL VALIDATION: {'✅ PASSED' if all(all_tests) else '❌ FAILED'}")
    print(f"   Tests passed: {sum(all_tests)}/{len(all_tests)}")
    
    return rs_scores, meta, returns_df

def demonstrate_interpretation():
    """Demonstrate how to interpret RS scores."""
    print(f"\n📚 RS SCORE INTERPRETATION GUIDE")
    print("=" * 40)
    
    print("🎯 What RS scores mean:")
    print("   1.0 = Best performer in the group (100th percentile)")
    print("   0.8 = Top 20% performer (80th percentile)")
    print("   0.5 = Median performer (50th percentile)")
    print("   0.2 = Bottom 20% performer (20th percentile)")
    print("   0.0 = Worst performer in the group (0th percentile)")
    
    print(f"\n🌍 Global RS Score:")
    print("   Compares ticker against ALL tickers in universe")
    print("   High global RS = outperforming most stocks")
    
    print(f"\n🎯 Theme RS Score:")
    print("   Compares ticker against tickers in SAME theme")
    print("   High theme RS = outperforming peers in same sector")
    
    print(f"\n🎪 Subtheme RS Score:")
    print("   Compares ticker against tickers in SAME subtheme")
    print("   High subtheme RS = outperforming direct competitors")
    
    print(f"\n💡 Strategic insights:")
    print("   • High Global + High Theme = Strong stock in strong sector")
    print("   • High Global + Low Theme = Strong stock in weak sector")
    print("   • Low Global + High Theme = Weak stock in strong sector")
    print("   • High Theme + Low Subtheme = Sector play, avoid specific niche")

def show_ticker_timeseries_example(rs_scores, meta):
    """Show example of the three time series per ticker."""
    print(f"\n📈 TICKER TIME SERIES EXAMPLE")
    print("=" * 40)
    
    # Pick first ticker
    ticker = list(rs_scores['global'].columns)[0]
    ticker_meta = meta.loc[ticker]
    
    print(f"🔍 Example ticker: {ticker}")
    print(f"   Theme: {ticker_meta['Theme']}")
    print(f"   Subtheme: {ticker_meta['Subtheme']}")
    
    print(f"\n📊 Three time series for {ticker}:")
    print(f"   Date        Global RS  Theme RS   Subtheme RS")
    print(f"   ----------  ---------  ---------  -----------")
    
    for date in rs_scores['global'].index:
        global_rs = rs_scores['global'].loc[date, ticker]
        theme_rs = rs_scores['theme'].loc[date, ticker]
        subtheme_rs = rs_scores['subtheme'].loc[date, ticker]
        
        print(f"   {date.strftime('%Y-%m-%d')}  {global_rs:9.4f}  {theme_rs:9.4f}  {subtheme_rs:11.4f}")
    
    print(f"\n💡 Interpretation for {ticker}:")
    latest_date = rs_scores['global'].index[-1]
    latest_global = rs_scores['global'].loc[latest_date, ticker]
    latest_theme = rs_scores['theme'].loc[latest_date, ticker]
    latest_subtheme = rs_scores['subtheme'].loc[latest_date, ticker]
    
    print(f"   Latest date: {latest_date.strftime('%Y-%m-%d')}")
    print(f"   Global RS: {latest_global:.4f} = {latest_global*100:.1f}th percentile vs ALL tickers")
    print(f"   Theme RS:  {latest_theme:.4f} = {latest_theme*100:.1f}th percentile vs {ticker_meta['Theme']} tickers")
    print(f"   Subtheme:  {latest_subtheme:.4f} = {latest_subtheme*100:.1f}th percentile vs {ticker_meta['Subtheme']} tickers")

if __name__ == "__main__":
    try:
        # Run validation
        rs_scores, meta, returns_df = validate_rs_calculation()
        
        # Show ticker time series example
        show_ticker_timeseries_example(rs_scores, meta)
        
        # Show interpretation guide
        demonstrate_interpretation()
        
        print(f"\n🎉 Validation completed!")
        print(f"\n📋 Key Points Validated:")
        print(f"   ✅ Each ticker has 3 separate RS time series")
        print(f"   ✅ All RS scores are 0-1 where 1=best, 0=worst")
        print(f"   ✅ Higher returns lead to higher RS scores")
        print(f"   ✅ Theme RS is relative within same theme")
        print(f"   ✅ Subtheme RS is relative within same subtheme")
        print(f"   ✅ Time series are consistent over time")
        
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        raise
