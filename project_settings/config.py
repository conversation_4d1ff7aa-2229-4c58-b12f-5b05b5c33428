import os
from pathlib import Path
from datetime import datetime
from typing import Optional
from dotenv import load_dotenv

# Root directory
BASE_DIR = Path(__file__).parent.parent.resolve()

# Load .env
env_path = BASE_DIR / '.env'
if env_path.exists():
    load_dotenv(dotenv_path=env_path)

# Base data directories
DATA_DIR = BASE_DIR / 'data'
RAW_BASE_DIR = DATA_DIR / 'raw'
PROCESSED_BASE_DIR = DATA_DIR / 'processed'
PROFILE_BASE_DIR = DATA_DIR / 'profiles'
LOG_DIR = BASE_DIR / 'logs'

# Legacy paths for backward compatibility
RAW_DIR = RAW_BASE_DIR
PROCESSED_DIR = PROCESSED_BASE_DIR
PROFILE_DIR = PROFILE_BASE_DIR

def get_date_string(date: Optional[datetime] = None) -> str:
    """Get date string in yyyy_mm_dd format."""
    if date is None:
        date = datetime.now()
    return date.strftime('%Y_%m_%d')

def get_raw_dir(date: Optional[datetime] = None) -> Path:
    """Get raw data directory for a specific date."""
    date_str = get_date_string(date)
    return RAW_BASE_DIR / date_str

def get_profile_dir(date: Optional[datetime] = None) -> Path:
    """Get profile directory for a specific date."""
    date_str = get_date_string(date)
    return PROFILE_BASE_DIR / date_str

def get_processed_dir(date: Optional[datetime] = None) -> Path:
    """Get processed directory for a specific date."""
    date_str = get_date_string(date)
    return PROCESSED_BASE_DIR / date_str

def get_most_recent_date_dir(base_dir: Path) -> Optional[Path]:
    """Find the most recent date directory in base_dir."""
    if not base_dir.exists():
        return None

    date_dirs = [d for d in base_dir.iterdir() if d.is_dir() and len(d.name) == 10 and d.name.count('_') == 2]
    if not date_dirs:
        return None

    # Sort by directory name (which is date in yyyy_mm_dd format)
    return max(date_dirs, key=lambda x: x.name)

# Ensure base dirs exist
for d in (RAW_BASE_DIR, PROCESSED_BASE_DIR, PROFILE_BASE_DIR, LOG_DIR):
    d.mkdir(parents=True, exist_ok=True)

# API key
_api_key_raw = os.getenv('FMP_API_KEY', '')
FMP_KEY = _api_key_raw.strip().strip('"').strip("'")
print(FMP_KEY)
