import os
from pathlib import Path
from dotenv import load_dotenv

# Root directory
BASE_DIR = Path(__file__).parent.parent.resolve()

# Load .env
env_path = BASE_DIR / '.env'
if env_path.exists():
    load_dotenv(dotenv_path=env_path)

# Paths
RAW_DIR = BASE_DIR / 'data' / 'raw'
PROCESSED_DIR = BASE_DIR / 'data' / 'processed'
PROFILE_DIR = BASE_DIR / 'data' / 'profiles'
LOG_DIR = BASE_DIR / 'logs'

# Ensure dirs exist
for d in (RAW_DIR, PROCESSED_DIR, PROFILE_DIR, LOG_DIR):
    d.mkdir(parents=True, exist_ok=True)

# API key
_api_key_raw = os.getenv('FMP_API_KEY', '')
FMP_KEY = _api_key_raw.strip().strip('"').strip("'")
print(FMP_KEY)
