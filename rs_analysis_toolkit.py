#!/usr/bin/env python3
"""
Relative Strength Analysis Toolkit
Provides comprehensive analysis tools for RS scores including time series tracking,
theme analysis, and performance attribution.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from backend.utils import load_latest_snapshot, get_available_dates, load_data_for_date
from project_settings.config import PROCESSED_BASE_DIR

class RSAnalyzer:
    """Comprehensive RS analysis toolkit."""
    
    def __init__(self, data_date: Optional[str] = None):
        """
        Initialize RS Analyzer.
        
        Args:
            data_date: Specific date to analyze (yyyy_mm_dd format). If None, uses latest.
        """
        self.data_date = data_date
        self.snapshot = None
        self.rs_timeseries = {}
        self._load_data()
    
    def _load_data(self):
        """Load RS data for analysis."""
        try:
            if self.data_date:
                print(f"📂 Loading data for {self.data_date}...")
                self.snapshot = load_data_for_date(self.data_date, 'processed', 'features_*.parquet')
                
                # Try to load enhanced RS timeseries
                try:
                    for rs_type in ['global', 'theme', 'subtheme']:
                        self.rs_timeseries[rs_type] = load_data_for_date(
                            self.data_date, 'processed', f'rs_{rs_type}_timeseries_*.parquet'
                        )
                except FileNotFoundError:
                    print("⚠️  Enhanced RS timeseries not found, using legacy data")
            else:
                print("📂 Loading latest data...")
                self.snapshot = load_latest_snapshot()
                print(f"✅ Loaded snapshot with {self.snapshot.shape[0]} tickers")
                
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            raise
    
    def get_top_performers(self, rs_type: str = 'rs_global', n: int = 10) -> pd.DataFrame:
        """Get top N performers by RS score."""
        if rs_type not in self.snapshot.columns:
            print(f"⚠️  {rs_type} not available, using rs_score")
            rs_type = 'rs_score'
        
        cols = ['Theme', 'Subtheme', rs_type]
        if 'close' in self.snapshot.columns:
            cols.append('close')
        if f'{rs_type}_pct' in self.snapshot.columns:
            cols.append(f'{rs_type}_pct')
        
        available_cols = [col for col in cols if col in self.snapshot.columns]
        return self.snapshot.nlargest(n, rs_type)[available_cols]
    
    def analyze_by_theme(self) -> pd.DataFrame:
        """Analyze RS performance by theme."""
        if 'Theme' not in self.snapshot.columns:
            print("⚠️  Theme data not available")
            return pd.DataFrame()
        
        rs_cols = [col for col in self.snapshot.columns if col.startswith('rs_') and not col.endswith('_pct')]
        
        theme_analysis = self.snapshot.groupby('Theme').agg({
            **{col: ['mean', 'std', 'min', 'max'] for col in rs_cols},
            'Theme': 'count'
        }).round(4)
        
        # Flatten column names
        theme_analysis.columns = ['_'.join(col).strip() if col[1] else col[0] for col in theme_analysis.columns]
        theme_analysis = theme_analysis.rename(columns={'Theme_count': 'ticker_count'})
        
        return theme_analysis.sort_values(f'{rs_cols[0]}_mean', ascending=False)
    
    def analyze_by_subtheme(self, theme: Optional[str] = None) -> pd.DataFrame:
        """Analyze RS performance by subtheme, optionally filtered by theme."""
        if 'Subtheme' not in self.snapshot.columns:
            print("⚠️  Subtheme data not available")
            return pd.DataFrame()
        
        data = self.snapshot
        if theme:
            data = data[data['Theme'] == theme]
            print(f"🎯 Analyzing subthemes within {theme}")
        
        rs_cols = [col for col in data.columns if col.startswith('rs_') and not col.endswith('_pct')]
        
        subtheme_analysis = data.groupby(['Theme', 'Subtheme']).agg({
            **{col: ['mean', 'std', 'count'] for col in rs_cols}
        }).round(4)
        
        # Flatten column names
        subtheme_analysis.columns = ['_'.join(col).strip() for col in subtheme_analysis.columns]
        
        return subtheme_analysis.sort_values(f'{rs_cols[0]}_mean', ascending=False)
    
    def find_rs_divergences(self, threshold: float = 0.3) -> pd.DataFrame:
        """Find tickers with divergent RS scores between global and theme/subtheme."""
        if not all(col in self.snapshot.columns for col in ['rs_global_pct', 'rs_theme_pct']):
            print("⚠️  Enhanced RS percentiles not available")
            return pd.DataFrame()
        
        # High theme RS but low global RS
        divergent = self.snapshot[
            (self.snapshot['rs_theme_pct'] > (1 - threshold)) & 
            (self.snapshot['rs_global_pct'] < threshold)
        ]
        
        if len(divergent) > 0:
            cols = ['Theme', 'Subtheme', 'rs_global_pct', 'rs_theme_pct', 'rs_subtheme_pct']
            available_cols = [col for col in cols if col in divergent.columns]
            return divergent[available_cols].sort_values('rs_theme_pct', ascending=False)
        
        return pd.DataFrame()
    
    def track_ticker_timeseries(self, ticker: str, rs_type: str = 'global') -> pd.DataFrame:
        """Track RS time series for a specific ticker."""
        if rs_type not in self.rs_timeseries:
            print(f"⚠️  {rs_type} timeseries not available")
            return pd.DataFrame()
        
        if ticker not in self.rs_timeseries[rs_type].columns:
            print(f"⚠️  {ticker} not found in {rs_type} timeseries")
            return pd.DataFrame()
        
        ts_data = self.rs_timeseries[rs_type][ticker].dropna()
        
        # Calculate rolling statistics
        result = pd.DataFrame({
            'rs_score': ts_data,
            'rs_rank_pct': ts_data.rolling(window=1).apply(
                lambda x: (self.rs_timeseries[rs_type].loc[x.index] <= x.iloc[0]).mean()
            ),
            'rs_ma_5': ts_data.rolling(5).mean(),
            'rs_ma_20': ts_data.rolling(20).mean(),
            'rs_volatility_10': ts_data.rolling(10).std()
        })
        
        return result
    
    def compare_themes_over_time(self, themes: List[str], rs_type: str = 'global') -> pd.DataFrame:
        """Compare theme performance over time."""
        if rs_type not in self.rs_timeseries:
            print(f"⚠️  {rs_type} timeseries not available")
            return pd.DataFrame()
        
        if 'Theme' not in self.snapshot.columns:
            print("⚠️  Theme data not available")
            return pd.DataFrame()
        
        theme_ts = {}
        for theme in themes:
            theme_tickers = self.snapshot[self.snapshot['Theme'] == theme].index
            available_tickers = [t for t in theme_tickers if t in self.rs_timeseries[rs_type].columns]
            
            if available_tickers:
                theme_ts[theme] = self.rs_timeseries[rs_type][available_tickers].mean(axis=1)
        
        return pd.DataFrame(theme_ts)
    
    def generate_rs_report(self) -> str:
        """Generate a comprehensive RS analysis report."""
        report = []
        report.append("📊 RELATIVE STRENGTH ANALYSIS REPORT")
        report.append("=" * 60)
        report.append(f"Data Date: {self.snapshot['data_date'].iloc[0] if 'data_date' in self.snapshot.columns else 'Unknown'}")
        report.append(f"Total Tickers: {len(self.snapshot)}")
        report.append("")
        
        # Top performers
        report.append("🏆 TOP 10 GLOBAL RS PERFORMERS:")
        top_performers = self.get_top_performers('rs_global', 10)
        report.append(top_performers.to_string())
        report.append("")
        
        # Theme analysis
        if 'Theme' in self.snapshot.columns:
            report.append("🎯 THEME ANALYSIS:")
            theme_analysis = self.analyze_by_theme()
            if not theme_analysis.empty:
                # Show key metrics
                key_cols = [col for col in theme_analysis.columns if 'mean' in col or 'ticker_count' in col]
                report.append(theme_analysis[key_cols].to_string())
            report.append("")
        
        # Divergences
        divergences = self.find_rs_divergences()
        if not divergences.empty:
            report.append("🔍 RS DIVERGENCES (High Theme RS, Low Global RS):")
            report.append(divergences.head().to_string())
            report.append("")
        
        # Summary statistics
        rs_cols = [col for col in self.snapshot.columns if col.startswith('rs_') and not col.endswith('_pct')]
        if rs_cols:
            report.append("📈 RS SCORE STATISTICS:")
            for col in rs_cols:
                stats = self.snapshot[col].describe()
                report.append(f"{col}: Mean={stats['mean']:.4f}, Std={stats['std']:.4f}, Range=[{stats['min']:.4f}, {stats['max']:.4f}]")
        
        return "\n".join(report)

def demonstrate_rs_analysis():
    """Demonstrate the RS analysis toolkit."""
    print("🚀 RS Analysis Toolkit Demo")
    print("=" * 50)
    
    try:
        # Initialize analyzer
        analyzer = RSAnalyzer()
        
        # Generate and display report
        report = analyzer.generate_rs_report()
        print(report)
        
        # Additional analysis examples
        print(f"\n🔍 ADDITIONAL ANALYSIS EXAMPLES:")
        print("-" * 40)
        
        # Theme comparison
        if 'Theme' in analyzer.snapshot.columns:
            themes = analyzer.snapshot['Theme'].unique()[:3]  # First 3 themes
            print(f"\n📊 Comparing themes: {list(themes)}")
            theme_comparison = analyzer.compare_themes_over_time(themes)
            if not theme_comparison.empty:
                print(f"Theme comparison data shape: {theme_comparison.shape}")
                print(theme_comparison.tail().round(4))
        
        # Individual ticker tracking
        if analyzer.rs_timeseries:
            sample_ticker = analyzer.snapshot.index[0]
            print(f"\n📈 Tracking {sample_ticker} RS timeseries:")
            ticker_ts = analyzer.track_ticker_timeseries(sample_ticker)
            if not ticker_ts.empty:
                print(ticker_ts.tail().round(4))
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise

if __name__ == "__main__":
    demonstrate_rs_analysis()
