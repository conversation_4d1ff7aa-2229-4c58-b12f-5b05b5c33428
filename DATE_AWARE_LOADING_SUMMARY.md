# 📅 Date-Aware RS Data Loading Implementation

## ✅ **Successfully Implemented Date-Aware Loading**

The relative strength module now requires explicit date specification when loading data, providing complete transparency about which dataset is being used.

## 🎯 **Key Features Implemented**

### **1. Mandatory Date Awareness**
- **Explicit date required** for loading RS data
- **Clear warning** when no date specified
- **Transparent folder indication** showing exactly which directory is being used

### **2. Date Format Validation**
- **Strict format**: `YYYY_MM_DD` (e.g., `'2025_06_28'`)
- **Clear error messages** for invalid formats
- **Helpful examples** in error messages

### **3. Available Dates Discovery**
- **`list_available_rs_dates()`** function to show all available dates
- **Sorted by date** (most recent first)
- **Indicates latest** dataset with marker

### **4. Enhanced Error Handling**
- **Missing date directories** show available alternatives
- **File not found** errors include helpful context
- **Clear guidance** on correct usage

## 🔧 **Implementation Details**

### **Updated `load_rs_data()` Function**
```python
def load_rs_data(
    date: Optional[str] = None,           # 🆕 Date parameter (YYYY_MM_DD)
    data_dir: Optional[Path] = None,      # Custom directory (optional)
    timestamp: Optional[str] = None       # Specific timestamp (optional)
) -> Dict[str, pd.DataFrame]:
```

**Behavior:**
- **With date**: Loads from `data/relative_strength/YYYY_MM_DD/`
- **Without date**: Shows warning and uses today's folder
- **Invalid date**: Raises `ValueError` with format guidance
- **Missing date**: Raises `FileNotFoundError` with available dates

### **New `list_available_rs_dates()` Function**
```python
def list_available_rs_dates() -> List[str]:
    """List all available RS data dates."""
```

**Output:**
```
📅 Available RS data dates:
   📍 (latest) 2025_06_29
      2025_06_28
      2025_06_27
```

### **Enhanced `RSAnalyzer.from_date()` Class Method**
```python
@classmethod
def from_date(cls, date: str, metadata: pd.DataFrame):
    """Create RSAnalyzer by loading RS data from a specific date."""
```

## 📊 **Test Results**

### **Date-Specific Loading:**
```
📅 Loading RS data from date: 2025_06_28
📁 Directory: .../data/relative_strength/2025_06_28
📂 Loaded global RS from rs_global_timeseries_20250628_282800.parquet
📂 Loaded theme RS from rs_theme_timeseries_20250628_282800.parquet
📂 Loaded subtheme RS from rs_subtheme_timeseries_20250628_282800.parquet
✅ Successfully loaded 3 RS time series from .../2025_06_28
📄 Files: ['rs_global_timeseries_*.parquet', ...]
```

### **Warning for No Date:**
```
⚠️  WARNING: No date specified! Loading from TODAY'S folder: .../2025_06_28
   To load from a specific date, use: load_rs_data(date='2025_06_28')
```

### **Error for Invalid Date:**
```
❌ Invalid date format: 2025-06-28. Expected format: YYYY_MM_DD (e.g., '2025_06_28')
```

### **Error for Missing Date:**
```
❌ RS data directory not found: .../2020_01_01
Available dates: ['2025_06_27', '2025_06_28', '2025_06_29']
```

## 🚀 **Usage Examples**

### **Recommended Usage (Explicit Date):**
```python
from relative_strength.utils import load_rs_data, list_available_rs_dates

# 1. Check available dates
available_dates = list_available_rs_dates()

# 2. Load specific date
rs_scores = load_rs_data(date='2025_06_28')

# 3. Create analyzer from date
analyzer = RSAnalyzer.from_date('2025_06_28', metadata)
```

### **Legacy Usage (Shows Warning):**
```python
# This will work but show warning
rs_scores = load_rs_data()  # ⚠️ WARNING: No date specified!
```

### **Custom Directory Usage:**
```python
# Load from custom directory
custom_dir = Path('custom/rs/location')
rs_scores = load_rs_data(data_dir=custom_dir)
```

## 📁 **Directory Structure**

```
data/
└── relative_strength/
    ├── 2025_06_27/
    │   ├── rs_global_timeseries_*.parquet
    │   ├── rs_theme_timeseries_*.parquet
    │   └── rs_subtheme_timeseries_*.parquet
    ├── 2025_06_28/
    │   ├── rs_global_timeseries_*.parquet
    │   ├── rs_theme_timeseries_*.parquet
    │   └── rs_subtheme_timeseries_*.parquet
    └── 2025_06_29/
        ├── rs_global_timeseries_*.parquet
        ├── rs_theme_timeseries_*.parquet
        └── rs_subtheme_timeseries_*.parquet
```

## 🎯 **Benefits**

### **1. Data Transparency**
- **Always know** which date's data you're loading
- **No ambiguity** about dataset source
- **Clear folder paths** displayed

### **2. Error Prevention**
- **Prevents accidental** loading of wrong date
- **Validates date format** before processing
- **Shows available alternatives** when date missing

### **3. Better Debugging**
- **Clear error messages** with helpful context
- **Explicit file paths** in loading messages
- **Easy to trace** data lineage

### **4. Professional Workflow**
- **Explicit is better than implicit** (Python principle)
- **Reproducible analysis** with date specification
- **Easy to compare** different dates

## 🔄 **Integration with Existing Code**

### **Feature Engineer (Automatic):**
```python
# Feature engineer automatically saves to today's date
fe = FeatureEngineer()
result = fe.compute(close_df, metadata)
# Saves to: data/relative_strength/2025_06_28/
```

### **Analysis Scripts (Manual):**
```python
# Analysis scripts should specify date explicitly
rs_scores = load_rs_data(date='2025_06_28')
analyzer = RSAnalyzer.from_date('2025_06_28', metadata)
```

### **Backward Compatibility:**
- **Old code still works** but shows warnings
- **Gradual migration** to explicit date usage
- **No breaking changes** to existing functionality

## 📋 **Updated Function Signatures**

### **Core Functions:**
```python
# Updated load function
load_rs_data(date='2025_06_28')                    # ✅ Recommended
load_rs_data()                                     # ⚠️ Shows warning

# New utility function
list_available_rs_dates()                          # ✅ New

# Enhanced analyzer
RSAnalyzer.from_date('2025_06_28', metadata)       # ✅ New class method
```

### **Export Updates:**
```python
from relative_strength import (
    RelativeStrengthCalculator,
    RSAnalyzer,
    load_rs_data,
    save_rs_data,
    list_available_rs_dates  # 🆕 New export
)
```

## 🎉 **Final Status**

**The relative strength module now provides complete data transparency!**

- 📅 **Date-aware loading** with explicit date specification
- ⚠️ **Clear warnings** when date not specified
- 🔍 **Transparent folder indication** showing exact data source
- ✅ **Format validation** with helpful error messages
- 📋 **Available dates discovery** for easy navigation
- 🔄 **Backward compatibility** with existing code

Users now have **complete control and transparency** over which RS dataset they're loading, eliminating any confusion about data sources!
