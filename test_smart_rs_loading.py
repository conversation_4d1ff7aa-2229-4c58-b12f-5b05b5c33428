#!/usr/bin/env python3
"""
Test the smart RS data loading functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
from datetime import datetime, timedelta

# Add paths for imports
sys.path.append('.')

from relative_strength.utils import (
    load_rs_data, 
    save_rs_data, 
    list_available_rs_dates, 
    smart_rs_date_selection
)
from relative_strength.calculator import RelativeStrengthCalculator

def create_test_rs_data_multiple_dates():
    """Create test RS data for multiple dates."""
    print("📊 Creating test RS data for multiple dates...")
    
    # Create test data
    dates = pd.date_range('2024-01-01', periods=10, freq='D')
    tickers = ['AAPL', 'MSFT', 'GOOGL', 'NVDA']
    
    metadata = pd.DataFrame({
        'Theme': ['Tech', 'Tech', 'Tech', 'Tech'],
        'Subtheme': ['Hardware', 'Software', 'Search', 'AI']
    }, index=tickers)
    
    # Create returns data
    np.random.seed(42)
    returns_data = {}
    for horizon in ['return_5d', 'return_20d']:
        returns_matrix = np.random.normal(0.01, 0.02, (10, 4))
        returns_data[horizon] = pd.DataFrame(
            returns_matrix, index=dates, columns=tickers
        )
    
    # Compute RS scores
    rs_calc = RelativeStrengthCalculator(
        horizons=['return_5d', 'return_20d'],
        weights={'return_5d': 0.4, 'return_20d': 0.6}
    )
    
    rs_scores = rs_calc.compute_all_rs_scores(returns_data, metadata)
    
    # Save data for multiple test dates
    test_dates = []
    today = datetime.now()
    
    # Create dates: 3 days ago, yesterday, today
    for days_back in [3, 1, 0]:
        test_date = (today - timedelta(days=days_back)).strftime('%Y_%m_%d')
        test_dates.append(test_date)
    
    saved_files_by_date = {}
    
    for i, test_date in enumerate(test_dates):
        print(f"\n💾 Saving RS data for {test_date}...")
        
        # Create custom directory for this test date
        from project_settings.config import RS_BASE_DIR
        test_date_dir = RS_BASE_DIR / test_date
        
        # Modify data slightly for each date to make them different
        modified_rs_scores = {}
        for rs_type, rs_df in rs_scores.items():
            if rs_type in ['global', 'theme', 'subtheme']:
                # Add small variation for each date
                variation = np.random.normal(0, 0.01, rs_df.shape)
                modified_rs_scores[rs_type] = rs_df + variation
                modified_rs_scores[rs_type] = modified_rs_scores[rs_type].clip(0, 1)  # Keep in 0-1 range
            else:
                modified_rs_scores[rs_type] = rs_df
        
        # Save with date-specific timestamp
        timestamp = f"{test_date.replace('_', '')}_{i:02d}0000"
        saved_files = save_rs_data(modified_rs_scores, test_date_dir, timestamp)
        saved_files_by_date[test_date] = saved_files
        
        print(f"✅ Saved {len(saved_files)} files for {test_date}")
    
    return test_dates

def test_smart_rs_loading():
    """Test the smart RS data loading functionality."""
    print("\n🧪 TESTING SMART RS DATA LOADING")
    print("=" * 60)
    
    # Create test data for multiple dates
    test_dates = create_test_rs_data_multiple_dates()
    
    # Test 1: List available dates with enhanced info
    print(f"\n📅 TEST 1: Enhanced date listing")
    print("-" * 40)
    
    available_dates = list_available_rs_dates(verbose=True)
    print(f"✅ Found {len(available_dates)} complete RS datasets")
    
    # Test 2: Smart date selection
    print(f"\n🧠 TEST 2: Smart date selection")
    print("-" * 40)
    
    try:
        smart_date = smart_rs_date_selection()
        print(f"✅ Smart selection chose: {smart_date}")
    except Exception as e:
        print(f"❌ Smart selection failed: {e}")
        return False
    
    # Test 3: Smart loading without date specification
    print(f"\n🎯 TEST 3: Smart loading (no date specified)")
    print("-" * 40)
    
    try:
        rs_scores_smart = load_rs_data()  # No date - should use smart selection
        print(f"✅ Smart loading successful:")
        for rs_type, rs_df in rs_scores_smart.items():
            print(f"   {rs_type}: {rs_df.shape}")
    except Exception as e:
        print(f"❌ Smart loading failed: {e}")
        return False
    
    # Test 4: Loading with specific date
    print(f"\n📅 TEST 4: Loading with specific date")
    print("-" * 40)
    
    for test_date in test_dates[:2]:  # Test first 2 dates
        try:
            print(f"\n🔍 Loading data for {test_date}:")
            rs_scores_dated = load_rs_data(date=test_date)
            print(f"✅ Successfully loaded data for {test_date}")
            
            # Validate data
            for rs_type, rs_df in rs_scores_dated.items():
                if rs_df.min().min() < 0 or rs_df.max().max() > 1:
                    print(f"   ⚠️  {rs_type} values outside 0-1 range")
                else:
                    print(f"   ✅ {rs_type} values in valid range")
                    
        except Exception as e:
            print(f"❌ Failed to load data for {test_date}: {e}")
    
    # Test 5: Smart fallback for missing date
    print(f"\n🔄 TEST 5: Smart fallback for missing date")
    print("-" * 40)
    
    try:
        missing_date = "2020_01_01"
        print(f"🔍 Attempting to load missing date: {missing_date}")
        rs_scores_fallback = load_rs_data(date=missing_date, smart_fallback=True)
        print(f"✅ Smart fallback successful - loaded alternative data")
        print(f"   Data shape: {rs_scores_fallback['global'].shape}")
    except Exception as e:
        print(f"❌ Smart fallback failed: {e}")
    
    # Test 6: Loading with smart_fallback=False (should fail for missing date)
    print(f"\n❌ TEST 6: No fallback for missing date")
    print("-" * 40)
    
    try:
        missing_date = "2020_01_01"
        rs_scores_no_fallback = load_rs_data(date=missing_date, smart_fallback=False)
        print(f"❌ Should have failed but didn't!")
    except FileNotFoundError as e:
        print(f"✅ Correctly failed without fallback: Expected behavior")
    except Exception as e:
        print(f"⚠️  Unexpected error: {e}")
    
    # Test 7: Performance comparison
    print(f"\n⏱️  TEST 7: Performance comparison")
    print("-" * 40)
    
    import time
    
    # Time smart loading
    start_time = time.time()
    rs_scores_perf1 = load_rs_data()
    smart_time = time.time() - start_time
    
    # Time specific date loading
    start_time = time.time()
    rs_scores_perf2 = load_rs_data(date=test_dates[0])
    specific_time = time.time() - start_time
    
    print(f"⏱️  Performance results:")
    print(f"   Smart loading: {smart_time:.3f}s")
    print(f"   Specific date: {specific_time:.3f}s")
    
    # Test 8: Data consistency check
    print(f"\n📊 TEST 8: Data consistency validation")
    print("-" * 40)
    
    # Load same date multiple times and check consistency
    rs_scores_1 = load_rs_data(date=test_dates[0])
    rs_scores_2 = load_rs_data(date=test_dates[0])
    
    consistent = True
    for rs_type in ['global', 'theme', 'subtheme']:
        if not rs_scores_1[rs_type].equals(rs_scores_2[rs_type]):
            consistent = False
            break
    
    if consistent:
        print(f"✅ Data loading is consistent")
    else:
        print(f"❌ Data loading inconsistency detected")
    
    return True

def cleanup_test_data():
    """Clean up test data."""
    try:
        from project_settings.config import RS_BASE_DIR
        today = datetime.now()
        
        # Clean up test dates
        for days_back in [3, 1, 0]:
            test_date = (today - timedelta(days=days_back)).strftime('%Y_%m_%d')
            test_date_dir = RS_BASE_DIR / test_date
            
            if test_date_dir.exists():
                for file in test_date_dir.glob("rs_*_timeseries_*.parquet"):
                    file.unlink()
                    print(f"🧹 Cleaned up {file.name}")
                
                # Remove directory if empty
                try:
                    test_date_dir.rmdir()
                    print(f"🧹 Removed directory {test_date}")
                except OSError:
                    pass  # Directory not empty
                    
    except Exception as e:
        print(f"⚠️  Cleanup warning: {e}")

if __name__ == "__main__":
    try:
        success = test_smart_rs_loading()
        if success:
            print("\n🎉 Smart RS loading test completed successfully!")
            print("\n📋 Key Features Demonstrated:")
            print("✅ Smart date selection (today > yesterday > latest)")
            print("✅ Enhanced date listing with completeness validation")
            print("✅ Smart fallback for missing dates")
            print("✅ Data validation and range checking")
            print("✅ Performance optimization")
            print("✅ Consistent data loading")
        else:
            print("\n❌ Smart RS loading test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        cleanup_test_data()
