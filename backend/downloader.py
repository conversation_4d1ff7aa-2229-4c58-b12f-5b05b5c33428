# downloader.py

import time
import pandas as pd
import requests
import logging
from typing import List, Optional
from datetime import datetime
from tqdm import tqdm
from project_settings.config import (
    get_raw_dir, get_profile_dir, get_most_recent_date_dir,
    RAW_BASE_DIR, PROFILE_BASE_DIR, FMP_KEY, LOG_DIR
)

# Setup logging
def setup_logging():
    """Setup logging for the downloader."""
    log_file = LOG_DIR / f"downloader_{datetime.now().strftime('%Y_%m_%d')}.log"

    # Create logger
    logger = logging.getLogger('downloader')
    logger.setLevel(logging.INFO)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create file handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

class DataDownloader:
    """Downloads and caches both OHLC data and company profiles for tickers."""

    def __init__(
        self,
        lookback: int = 365,
        price_freshness_hours: int = 24,
        profile_freshness_hours: int = 24,
        enable_logging: bool = True
    ):
        self.lookback = lookback
        self.price_ttl = price_freshness_hours * 3600
        self.profile_ttl = profile_freshness_hours * 3600

        # Setup logging
        if enable_logging:
            self.logger = setup_logging()
        else:
            self.logger = logging.getLogger('downloader')
            self.logger.addHandler(logging.NullHandler())

    def fetch_ohlc(self, ticker: str) -> pd.DataFrame:
        self.logger.info(f"Fetching OHLC data for {ticker}")

        # Check for cached data in today's directory first
        today_raw_dir = get_raw_dir()
        cache = today_raw_dir / f"{ticker}.parquet"

        # If today's cache exists and is fresh, use it
        if cache.exists() and (time.time() - cache.stat().st_mtime) < self.price_ttl:
            self.logger.info(f"Using cached OHLC data for {ticker} from {cache}")
            return pd.read_parquet(cache, engine="fastparquet")

        # Check recent date directories for cached data
        recent_dir = get_most_recent_date_dir(RAW_BASE_DIR)
        if recent_dir and recent_dir != today_raw_dir:
            recent_cache = recent_dir / f"{ticker}.parquet"
            if recent_cache.exists() and (time.time() - recent_cache.stat().st_mtime) < self.price_ttl:
                self.logger.info(f"Using cached OHLC data for {ticker} from recent directory {recent_cache}")
                return pd.read_parquet(recent_cache, engine="fastparquet")

        # Fetch from API
        self.logger.info(f"Downloading OHLC data for {ticker} from API")
        print(f"📈 Downloading OHLC data for {ticker}...")

        url = (
            f"https://financialmodelingprep.com/api/v3/historical-price-full/{ticker}?"
            f"timeseries={self.lookback}&apikey={FMP_KEY}"
        )

        try:
            r = requests.get(url, timeout=30)
            r.raise_for_status()
            raw = r.json().get("historical", [])
            self.logger.info(f"Successfully downloaded {len(raw)} records for {ticker}")
        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to download OHLC data for {ticker}: {e}"
            self.logger.error(error_msg)
            print(f"❌ {error_msg}")
            raise ValueError(error_msg)

        # Build DataFrame
        df = pd.DataFrame(raw)
        # Double-check DataFrame
        if df.empty:
            msg = f"Parsed DataFrame for ticker '{ticker}' is empty after formatting"
            self.logger.error(msg)
            print(f"❌ {msg}")
            raise ValueError(msg)

        df["date"] = pd.to_datetime(df["date"])
        df = df.set_index("date").sort_index()

        # Check for empty data and raise exception
        if df.empty:
            msg = f"No OHLC data found for ticker '{ticker}' over the last {self.lookback} days"
            self.logger.error(msg)
            print(f"❌ {msg}")
            raise ValueError(msg)

        # Cache in today's directory and return
        today_raw_dir.mkdir(parents=True, exist_ok=True)
        df.to_parquet(cache, engine="fastparquet")
        self.logger.info(f"Cached OHLC data for {ticker} to {cache}")
        print(f"✅ Successfully downloaded and cached OHLC data for {ticker}")
        return df

    def fetch_close_df(self, tickers: List[str]) -> pd.DataFrame:
        self.logger.info(f"Starting to fetch close prices for {len(tickers)} tickers")
        print(f"\n🚀 Fetching close prices for {len(tickers)} tickers...")

        frames = []
        successful_downloads = 0
        failed_downloads = 0

        # Use tqdm for progress bar
        for t in tqdm(tickers, desc="Downloading OHLC", unit="ticker"):
            try:
                df = self.fetch_ohlc(t)
                frames.append(df["close"].rename(t))
                successful_downloads += 1
            except ValueError as e:
                error_msg = f"Skipping {t}: {e}"
                self.logger.warning(error_msg)
                print(f"⚠️  {error_msg}")
                failed_downloads += 1
                continue

        self.logger.info(f"OHLC download completed: {successful_downloads} successful, {failed_downloads} failed")
        print(f"\n📊 OHLC download summary: {successful_downloads} successful, {failed_downloads} failed")

        if not frames:
            error_msg = "No successful OHLC downloads"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        result_df = pd.concat(frames, axis=1)
        self.logger.info(f"Combined close DataFrame shape: {result_df.shape}")
        return result_df

    def fetch_company_profiles(self, tickers: List[str]) -> pd.DataFrame:
        """
        Caches each ticker's profile in date-based profile directories
        with columns: companyName, beta, marketCap, profile_date.
        """
        self.logger.info(f"Starting to fetch company profiles for {len(tickers)} tickers")
        print(f"\n🏢 Fetching company profiles for {len(tickers)} tickers...")

        recs = []
        today_profile_dir = get_profile_dir()
        successful_downloads = 0
        failed_downloads = 0
        cached_profiles = 0

        # Use tqdm for progress bar
        for t in tqdm(tickers, desc="Downloading Profiles", unit="ticker"):
            try:
                # Check today's directory first
                cache = today_profile_dir / f"{t}.parquet"

                if cache.exists() and (time.time() - cache.stat().st_mtime) < self.profile_ttl:
                    self.logger.info(f"Using cached profile for {t} from {cache}")
                    prof = pd.read_parquet(cache, engine="fastparquet")
                    cached_profiles += 1
                else:
                    # Check recent date directories for cached data
                    recent_dir = get_most_recent_date_dir(PROFILE_BASE_DIR)
                    if recent_dir and recent_dir != today_profile_dir:
                        recent_cache = recent_dir / f"{t}.parquet"
                        if recent_cache.exists() and (time.time() - recent_cache.stat().st_mtime) < self.profile_ttl:
                            self.logger.info(f"Using cached profile for {t} from recent directory {recent_cache}")
                            prof = pd.read_parquet(recent_cache, engine="fastparquet")
                            cached_profiles += 1
                            recs.append(prof)
                            continue

                    # Fetch from API
                    self.logger.info(f"Downloading profile for {t} from API")
                    print(f"🔍 Downloading profile for {t}...")

                    url = f"https://financialmodelingprep.com/api/v3/profile/{t}?apikey={FMP_KEY}"

                    try:
                        r = requests.get(url, timeout=30)
                        r.raise_for_status()
                        data = r.json()

                        if not data:
                            self.logger.warning(f"No profile data returned for {t}")
                            print(f"⚠️  No profile data found for {t}")
                            failed_downloads += 1
                            continue

                        d = data[0]
                        prof = pd.DataFrame([{
                            "companyName": d.get("companyName"),
                            "beta": d.get("beta"),
                            "marketCap": d.get("mktCap"),
                            "profile_date": pd.Timestamp.now()
                        }], index=[t])

                        # Save to today's directory
                        today_profile_dir.mkdir(parents=True, exist_ok=True)
                        prof.to_parquet(cache, engine="fastparquet")
                        self.logger.info(f"Cached profile for {t} to {cache}")
                        print(f"✅ Successfully downloaded and cached profile for {t}")
                        successful_downloads += 1

                    except requests.exceptions.RequestException as e:
                        error_msg = f"Failed to download profile for {t}: {e}"
                        self.logger.error(error_msg)
                        print(f"❌ {error_msg}")
                        failed_downloads += 1
                        continue

                recs.append(prof)

            except Exception as e:
                error_msg = f"Unexpected error processing profile for {t}: {e}"
                self.logger.error(error_msg)
                print(f"❌ {error_msg}")
                failed_downloads += 1
                continue

        self.logger.info(f"Profile download completed: {successful_downloads} downloaded, {cached_profiles} cached, {failed_downloads} failed")
        print(f"\n📋 Profile summary: {successful_downloads} downloaded, {cached_profiles} cached, {failed_downloads} failed")

        if recs:
            result_df = pd.concat(recs)
            self.logger.info(f"Combined profile DataFrame shape: {result_df.shape}")
            return result_df

        self.logger.warning("No profile data collected")
        return pd.DataFrame(columns=["companyName","beta","marketCap","profile_date"])
