# main.py

import pandas as pd
from pathlib import Path
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.downloader import DataDownloader
from backend.feature_engineer import FeatureEngineer
from project_settings.config import get_processed_dir, get_raw_dir, get_profile_dir
from relative_strength.utils import load_rs_data, smart_rs_date_selection, list_available_rs_dates

def find_latest_data_date() -> str:
    """
    Find the most recent date with data in YYYY_MM_DD format.

    Returns:
        Latest date string or None if no data found
    """
    # Check raw data directory for latest date
    raw_base = get_raw_dir().parent  # Get base raw directory
    profile_base = get_profile_dir().parent  # Get base profile directory

    available_dates = set()

    # Check raw data dates
    if raw_base.exists():
        for date_dir in raw_base.iterdir():
            if date_dir.is_dir() and any(date_dir.glob("*.parquet")):
                available_dates.add(date_dir.name)

    # Check profile data dates
    if profile_base.exists():
        for date_dir in profile_base.iterdir():
            if date_dir.is_dir() and any(date_dir.glob("*.parquet")):
                available_dates.add(date_dir.name)

    if available_dates:
        latest_date = max(available_dates)
        print(f"📅 Found latest data date: {latest_date}")
        return latest_date
    else:
        print(f"📅 No existing data found")
        return None

def load_existing_data(date: str, tickers: list) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load existing price and profile data from a specific date.

    Args:
        date: Date string in YYYY_MM_DD format
        tickers: List of ticker symbols to load

    Returns:
        Tuple of (close_df, profiles)
    """
    print(f"📂 Loading existing data from {date}...")

    # Get date-specific directories
    from datetime import datetime
    date_obj = datetime.strptime(date, '%Y_%m_%d')
    raw_dir = get_raw_dir(date_obj)
    profile_dir = get_profile_dir(date_obj)

    print(f"   Raw data directory: {raw_dir}")
    print(f"   Profile directory: {profile_dir}")

    # Load price data
    close_df = None
    raw_files = list(raw_dir.glob("*.parquet")) if raw_dir.exists() else []

    if raw_files:
        print(f"   Found {len(raw_files)} price data files")

        # Check if files are individual ticker files or combined files
        sample_file = raw_files[0]
        sample_df = pd.read_parquet(sample_file, engine='fastparquet')

        if len(sample_df.columns) == 1 or sample_file.stem in tickers:
            # Individual ticker files - combine them
            print(f"   Loading individual ticker files...")

            ticker_dfs = {}
            loaded_tickers = []

            for ticker in tickers:
                ticker_file = raw_dir / f"{ticker}.parquet"
                if ticker_file.exists():
                    try:
                        ticker_df = pd.read_parquet(ticker_file, engine='fastparquet')
                        # Assume the price column is 'close' or the first numeric column
                        if 'close' in ticker_df.columns:
                            ticker_dfs[ticker] = ticker_df['close']
                        else:
                            # Find first numeric column
                            numeric_cols = ticker_df.select_dtypes(include=[float, int]).columns
                            if len(numeric_cols) > 0:
                                ticker_dfs[ticker] = ticker_df[numeric_cols[0]]
                            else:
                                print(f"   ⚠️  No numeric columns found in {ticker}.parquet")
                        loaded_tickers.append(ticker)
                    except Exception as e:
                        print(f"   ⚠️  Failed to load {ticker}.parquet: {e}")

            if ticker_dfs:
                close_df = pd.DataFrame(ticker_dfs)
                print(f"   Combined price data: {close_df.shape} (dates x tickers)")
                print(f"   Loaded tickers: {loaded_tickers[:5]}{'...' if len(loaded_tickers) > 5 else ''}")
            else:
                raise FileNotFoundError(f"No valid ticker data could be loaded from {raw_dir}")

        else:
            # Combined file - use existing logic
            print(f"   Loading combined price file...")
            latest_raw_file = max(raw_files, key=lambda p: p.stat().st_mtime)

            try:
                close_df = pd.read_parquet(latest_raw_file, engine='fastparquet')

                # Filter to requested tickers
                available_tickers = [t for t in tickers if t in close_df.columns]
                missing_tickers = [t for t in tickers if t not in close_df.columns]

                if missing_tickers:
                    print(f"   ⚠️  Missing tickers: {missing_tickers[:5]}{'...' if len(missing_tickers) > 5 else ''}")

                close_df = close_df[available_tickers]
                print(f"   Filtered price data: {close_df.shape} (dates x tickers)")

            except Exception as e:
                raise FileNotFoundError(f"Failed to load price data from {latest_raw_file}: {e}")
    else:
        raise FileNotFoundError(f"No price data found in {raw_dir}")

    # Load profile data
    profiles = pd.DataFrame()
    profile_files = list(profile_dir.glob("*.parquet")) if profile_dir.exists() else []

    if profile_files:
        print(f"   Found {len(profile_files)} profile files")

        # Load individual profile files and combine with ticker from filename
        profile_data = {}
        loaded_profile_tickers = []

        for profile_file in profile_files:
            ticker = profile_file.stem  # Get ticker from filename (e.g., 'AAPL.parquet' -> 'AAPL')

            if ticker in tickers:  # Only load requested tickers
                try:
                    df = pd.read_parquet(profile_file, engine='fastparquet')

                    # Take the first row (should be only one row per ticker)
                    if len(df) > 0:
                        profile_data[ticker] = df.iloc[0].to_dict()
                        loaded_profile_tickers.append(ticker)

                except Exception as e:
                    print(f"   ⚠️  Failed to load profile for {ticker}: {e}")

        if profile_data:
            # Create DataFrame from profile data with ticker as index
            profiles = pd.DataFrame.from_dict(profile_data, orient='index')
            print(f"   Loaded profiles: {len(profiles)} companies")
            print(f"   Profile columns: {list(profiles.columns)}")
            print(f"   Loaded profile tickers: {loaded_profile_tickers[:5]}{'...' if len(loaded_profile_tickers) > 5 else ''}")
        else:
            print(f"   ⚠️  No valid profile data could be loaded")
            profiles = pd.DataFrame(index=tickers)

    else:
        print(f"   ⚠️  No profile data found in {profile_dir}")
        # Create empty profiles DataFrame with correct index
        profiles = pd.DataFrame(index=tickers)

    return close_df, profiles

def smart_data_loading(tickers: list, lookback: int = 365, force_download: bool = False) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Smart data loading that uses existing data when available.

    Args:
        tickers: List of ticker symbols
        lookback: Number of days of historical data
        force_download: If True, force fresh download

    Returns:
        Tuple of (close_df, profiles)
    """
    print(f"\n📊 Smart data loading for {len(tickers)} tickers...")

    if force_download:
        print("🔄 Force download requested - fetching fresh data...")
        should_download = True
        latest_date = None
    else:
        # Check for existing data
        latest_date = find_latest_data_date()

        if latest_date:
            # Check if data is from today
            today = datetime.now().strftime('%Y_%m_%d')

            if latest_date == today:
                print(f"✅ Found fresh data from today ({latest_date})")
                should_download = False
            else:
                print(f"⚠️  Latest data is from {latest_date} (not today)")
                print(f"   Will download fresh data for today")
                should_download = True
        else:
            print(f"📥 No existing data found - will download fresh data")
            should_download = True

    if should_download:
        # Download fresh data
        print("🔄 Downloading fresh data...")
        dl = DataDownloader(
            lookback=lookback,
            price_freshness_hours=1,  # Force fresh download
            profile_freshness_hours=1
        )

        close_df = dl.fetch_close_df(tickers)
        profiles = dl.fetch_company_profiles(tickers)

        print(f"✅ Downloaded fresh data:")
        print(f"   Price data: {close_df.shape} (dates x tickers)")
        print(f"   Profiles: {len(profiles)} companies")

    else:
        # Load existing data
        try:
            close_df, profiles = load_existing_data(latest_date, tickers)
            print(f"✅ Loaded existing data from {latest_date}")
        except Exception as e:
            print(f"❌ Failed to load existing data: {e}")
            print(f"🔄 Falling back to fresh download...")

            dl = DataDownloader(
                lookback=lookback,
                price_freshness_hours=1,
                profile_freshness_hours=1
            )

            close_df = dl.fetch_close_df(tickers)
            profiles = dl.fetch_company_profiles(tickers)

            print(f"✅ Downloaded fresh data as fallback")

    return close_df, profiles

def smart_rs_loading(use_existing_rs: bool = True, rs_date: str = None) -> dict:
    """
    Smart RS data loading using the enhanced RS module functions.

    Args:
        use_existing_rs: If True, try to use existing RS data
        rs_date: Specific RS date to load (YYYY_MM_DD format)

    Returns:
        Dict with RS info or empty dict if not found/not requested
    """
    if not use_existing_rs:
        print(f"🎯 RS loading disabled - will compute fresh RS scores")
        return {}

    print(f"\n🧠 Smart RS data loading...")

    try:
        # Use the smart RS loading functions from the RS module
        if rs_date:
            print(f"📅 Loading RS data for specific date: {rs_date}")
            rs_scores = load_rs_data(date=rs_date, smart_fallback=True)
            return {
                'date': rs_date,
                'rs_scores': rs_scores,
                'source': 'specific_date'
            }
        else:
            # Use smart date selection
            print(f"🎯 Using smart RS date selection...")
            rs_scores = load_rs_data()  # Smart selection
            selected_date = smart_rs_date_selection()
            return {
                'date': selected_date,
                'rs_scores': rs_scores,
                'source': 'smart_selection'
            }

    except Exception as e:
        print(f"⚠️  Could not load existing RS data: {e}")
        print(f"   Will compute fresh RS scores instead")
        return {}

def main(
    input_csv: str,
    output_path: str = None,
    lookback: int = 365,
    close_horizons=None,
    rs_horizons=None,
    weights=None,
    sma_windows=None,
    force_download: bool = False,
    use_existing_rs: bool = True,
    rs_date: str = None
):
    """
    Main backend pipeline with smart data loading.

    Args:
        input_csv: Path to CSV file with ticker metadata
        output_path: Optional custom output path
        lookback: Number of days of historical data
        close_horizons: Return calculation horizons
        rs_horizons: RS calculation horizons
        weights: RS weights
        sma_windows: SMA windows
        force_download: If True, force fresh data download
        use_existing_rs: If True, try to use existing RS data
        rs_date: Specific RS date to load (YYYY_MM_DD format)
    """
    print("🚀 BACKEND PIPELINE WITH SMART DATA LOADING")
    print("=" * 60)

    # 1) Load metadata
    print(f"📂 Loading ticker metadata from {input_csv}...")
    if not Path(input_csv).exists():
        raise FileNotFoundError(f"Input CSV not found: {input_csv}")

    meta = pd.read_csv(input_csv).set_index("ticker")
    meta = meta[~meta.index.duplicated()]
    print(f"✅ Loaded {len(meta)} unique tickers")

    # 2) Smart data loading
    close_df, profiles = smart_data_loading(
        tickers=meta.index.tolist(),
        lookback=lookback,
        force_download=force_download
    )

    # 3) Smart RS data loading
    existing_rs_info = smart_rs_loading(
        use_existing_rs=use_existing_rs,
        rs_date=rs_date
    )

    # 4) Smart feature computation with RS integration
    should_compute_rs = True
    rs_files_info = {}

    if existing_rs_info:
        print(f"\n🤔 Deciding whether to use existing RS data or compute fresh...")

        # Check if existing RS data is recent enough
        existing_date = existing_rs_info['date']
        today = datetime.now().strftime('%Y_%m_%d')

        if existing_date == today:
            print(f"✅ Found fresh RS data from today ({existing_date})")
            print(f"   Skipping RS computation - will use existing data")
            should_compute_rs = False
        else:
            print(f"⚠️  Existing RS data is from {existing_date} (not today)")
            print(f"   Will compute fresh RS scores")

    print(f"\n⚙️  Computing features for {close_df.shape[1]} tickers with {close_df.shape[0]} days of data...")
    fe = FeatureEngineer(
        close_horizons=close_horizons,
        rs_horizons=rs_horizons,
        weights=weights,
        sma_windows=sma_windows
    )

    if should_compute_rs:
        # Compute fresh features with RS
        print(f"🔄 Computing fresh features with RS scores...")
        res = fe.compute(close_df, meta)  # Pass metadata for RS calculation
        snapshot = res.snapshot

        if res.rs_scores is not None:
            print(f"🎯 Fresh RS scores computed:")
            for rs_type in ['global', 'theme', 'subtheme']:
                if f'rs_{rs_type}' in snapshot.columns:
                    rs_stats = snapshot[f'rs_{rs_type}'].describe()
                    print(f"   {rs_type.capitalize()} RS: mean={rs_stats['mean']:.3f}, std={rs_stats['std']:.3f}")

            if res.rs_files:
                rs_files_info = res.rs_files
                print(f"💾 RS time series files saved:")
                for rs_type, filepath in res.rs_files.items():
                    print(f"   {rs_type}: {filepath.name}")
    else:
        # Use existing RS data - compute basic features only
        print(f"📊 Computing basic features (using existing RS data)...")
        res = fe.compute(close_df, None)  # No metadata = no RS computation
        snapshot = res.snapshot

        # Add existing RS data to snapshot
        existing_rs = existing_rs_info['rs_scores']
        latest_date = existing_rs['global'].index.max()

        print(f"📊 Adding existing RS data from {existing_rs_info['date']}...")
        snapshot['rs_global'] = existing_rs['global'].loc[latest_date]
        snapshot['rs_theme'] = existing_rs['theme'].loc[latest_date]
        snapshot['rs_subtheme'] = existing_rs['subtheme'].loc[latest_date]

        # Add percentile columns
        snapshot['rs_global_pct'] = snapshot['rs_global'] * 100
        snapshot['rs_theme_pct'] = snapshot['rs_theme'] * 100
        snapshot['rs_subtheme_pct'] = snapshot['rs_subtheme'] * 100

        print(f"✅ Combined basic features with existing RS data from {existing_rs_info['date']}")

    print(f"✅ Feature computation completed. Snapshot shape: {snapshot.shape}")

    # 4) Create dashboard
    print(f"\n🔗 Creating dashboard...")

    # Remove duplicate columns from snapshot (Theme, Subtheme already in meta)
    snapshot_clean = snapshot.drop(columns=['Theme', 'Subtheme'], errors='ignore')

    dashboard = (
        meta
        .join(profiles, how="left")
        .join(snapshot_clean, how="inner")
    )
    print(f"✅ Dashboard created with {dashboard.shape[0]} tickers and {dashboard.shape[1]} columns")

    # 5) Save dashboard
    print(f"\n💾 Saving dashboard...")
    if output_path is None:
        today_processed_dir = get_processed_dir()
        ts = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = today_processed_dir / f"dashboard_{ts}.parquet"
    else:
        output_path = Path(output_path)

    dashboard.to_parquet(output_path, engine="fastparquet")
    print(f"🚀 Dashboard written to: {output_path}")

    # Show summary
    print(f"\n📋 BACKEND PIPELINE SUMMARY")
    print("=" * 40)
    print(f"✅ Processed {len(meta)} tickers")
    print(f"✅ Loaded {close_df.shape[0]} days of price data")
    print(f"✅ Created dashboard with {dashboard.shape[1]} features")

    if 'rs_global' in dashboard.columns:
        print(f"✅ Enhanced RS scores included")
        print(f"\n🏆 Top 3 performers by Global RS:")
        top_3 = dashboard.nlargest(3, 'rs_global')[['Theme', 'Subtheme', 'rs_global']]
        print(top_3.round(4).to_string())

    return dashboard, output_path

if __name__ == "__main__":
    # Default configuration
    print("🚀 Starting Stealth Agency Trading Pipeline...")
    print("=" * 60)

    # You can customize these parameters as needed
    config = {
        "input_csv": "default_tickers.csv",
        "lookback": 365,
        "close_horizons": ['return_1d', 'return_20d', 'return_22d', 'return_200d'],
        "rs_horizons": ['return_20d', 'return_22d', 'return_200d'],
        "weights": None,  # Will use default equal weights
        "sma_windows": [5, 10, 20, 120]
    }

    print(f"📋 Configuration:")
    print(f"   Input CSV: {config['input_csv']}")
    print(f"   Lookback days: {config['lookback']}")
    print(f"   Close horizons: {config['close_horizons']}")
    print(f"   RS horizons: {config['rs_horizons']}")
    print(f"   SMA windows: {config['sma_windows']}")
    print()

    try:
        # Run the main pipeline with smart data loading
        dashboard, output_path = main(
            input_csv=config["input_csv"],
            lookback=config["lookback"],
            close_horizons=config["close_horizons"],
            rs_horizons=config["rs_horizons"],
            weights=config["weights"],
            sma_windows=config["sma_windows"],
            force_download=False  # Use existing data when available
        )

        print(f"\n📊 Final Results:")
        print(f"   Dashboard shape: {dashboard.shape}")
        print(f"   Output file: {output_path}")

        print("\n" + "=" * 60)
        print("🎉 Pipeline completed successfully!")
        print("📁 Check the data/processed/yyyy_mm_dd/ directory for output files")
        print("📊 Check the logs/ directory for detailed logs")

    except Exception as e:
        print(f"\n❌ Pipeline failed with error: {e}")
        print("📋 Check the logs for more details")
        raise