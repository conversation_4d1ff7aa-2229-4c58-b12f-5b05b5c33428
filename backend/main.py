# main.py

import pandas as pd
from pathlib import Path
from downloader import DataDownloader
from feature_engineer import FeatureEngineer
from project_settings.config import get_processed_dir

def main(
    input_csv: str,
    output_path: str = None,
    lookback: int = 365,
    close_horizons=None,
    rs_horizons=None,
    weights=None,
    sma_windows=None
):
    # 1) Load your base CSV (must include columns: Theme, Subtheme, ticker)
    meta = pd.read_csv(input_csv).set_index("ticker")
    meta = meta[~meta.index.duplicated()]

    # 2) Download prices + profiles
    print(f"🔄 Initializing DataDownloader...")
    dl = DataDownloader(
        lookback=lookback,
        price_freshness_hours=24,
        profile_freshness_hours=24
    )

    print(f"📊 Processing {len(meta)} unique tickers...")
    close_df = dl.fetch_close_df(meta.index.tolist())
    profiles = dl.fetch_company_profiles(meta.index.tolist())

    # 3) Compute features
    print(f"\n⚙️  Computing features for {close_df.shape[1]} tickers with {close_df.shape[0]} days of data...")
    fe = FeatureEngineer(
        close_horizons=close_horizons,
        rs_horizons=rs_horizons,
        weights=weights,
        sma_windows=sma_windows
    )
    res = fe.compute(close_df)
    snapshot = res.snapshot  # has 'data_date'
    print(f"✅ Feature computation completed. Snapshot shape: {snapshot.shape}")

    # 4) Inner-join to ensure only tickers with valid features appear
    print(f"\n🔗 Joining metadata, profiles, and features...")
    dashboard = (
        meta
        .join(profiles, how="left")
        .join(snapshot, how="inner")
    )
    print(f"✅ Dashboard created with {dashboard.shape[0]} tickers and {dashboard.shape[1]} columns")

    # 5) Write out
    if output_path is None:
        ts = res.snapshot_file.stem.split("_")[-1]
        today_processed_dir = get_processed_dir()
        output_path = today_processed_dir / f"dashboard_{ts}.parquet"
    else:
        output_path = Path(output_path)

    dashboard.to_parquet(output_path, engine="fastparquet")
    print(f"🚀 Dashboard written to: {output_path}")

if __name__ == "__main__":
    # Default configuration
    print("🚀 Starting Stealth Agency Trading Pipeline...")
    print("=" * 60)

    # You can customize these parameters as needed
    config = {
        "input_csv": "default_tickers.csv",
        "lookback": 365,
        "close_horizons": ['return_1d', 'return_20d', 'return_22d', 'return_200d'],
        "rs_horizons": ['return_20d', 'return_22d', 'return_200d'],
        "weights": None,  # Will use default equal weights
        "sma_windows": [5, 10, 20, 120]
    }

    print(f"📋 Configuration:")
    print(f"   Input CSV: {config['input_csv']}")
    print(f"   Lookback days: {config['lookback']}")
    print(f"   Close horizons: {config['close_horizons']}")
    print(f"   RS horizons: {config['rs_horizons']}")
    print(f"   SMA windows: {config['sma_windows']}")
    print()

    try:
        # Run the main pipeline
        main(
            input_csv=config["input_csv"],
            lookback=config["lookback"],
            close_horizons=config["close_horizons"],
            rs_horizons=config["rs_horizons"],
            weights=config["weights"],
            sma_windows=config["sma_windows"]
        )

        print("\n" + "=" * 60)
        print("🎉 Pipeline completed successfully!")
        print("📁 Check the data/processed/yyyy_mm_dd/ directory for output files")
        print("📊 Check the logs/ directory for detailed logs")

    except Exception as e:
        print(f"\n❌ Pipeline failed with error: {e}")
        print("📋 Check the logs for more details")
        raise