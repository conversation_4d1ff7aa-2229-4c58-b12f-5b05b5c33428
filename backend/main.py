# main.py

import pandas as pd
from pathlib import Path
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.downloader import DataDownloader
from backend.feature_engineer import FeatureEngineer
from project_settings.config import get_processed_dir, get_raw_dir, get_profile_dir

def find_latest_data_date() -> str:
    """
    Find the most recent date with data in YYYY_MM_DD format.

    Returns:
        Latest date string or None if no data found
    """
    # Check raw data directory for latest date
    raw_base = get_raw_dir().parent  # Get base raw directory
    profile_base = get_profile_dir().parent  # Get base profile directory

    available_dates = set()

    # Check raw data dates
    if raw_base.exists():
        for date_dir in raw_base.iterdir():
            if date_dir.is_dir() and any(date_dir.glob("*.parquet")):
                available_dates.add(date_dir.name)

    # Check profile data dates
    if profile_base.exists():
        for date_dir in profile_base.iterdir():
            if date_dir.is_dir() and any(date_dir.glob("*.parquet")):
                available_dates.add(date_dir.name)

    if available_dates:
        latest_date = max(available_dates)
        print(f"📅 Found latest data date: {latest_date}")
        return latest_date
    else:
        print(f"📅 No existing data found")
        return None

def load_existing_data(date: str, tickers: list) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load existing price and profile data from a specific date.

    Args:
        date: Date string in YYYY_MM_DD format
        tickers: List of ticker symbols to load

    Returns:
        Tuple of (close_df, profiles)
    """
    print(f"📂 Loading existing data from {date}...")

    # Get date-specific directories
    from datetime import datetime
    date_obj = datetime.strptime(date, '%Y_%m_%d')
    raw_dir = get_raw_dir(date_obj)
    profile_dir = get_profile_dir(date_obj)

    print(f"   Raw data directory: {raw_dir}")
    print(f"   Profile directory: {profile_dir}")

    # Load price data
    close_df = None
    raw_files = list(raw_dir.glob("*.parquet")) if raw_dir.exists() else []

    if raw_files:
        # Find the most recent price file
        latest_raw_file = max(raw_files, key=lambda p: p.stat().st_mtime)
        print(f"   Loading price data from: {latest_raw_file.name}")
        close_df = pd.read_parquet(latest_raw_file, engine='fastparquet')

        # Filter to requested tickers
        available_tickers = [t for t in tickers if t in close_df.columns]
        close_df = close_df[available_tickers]
        print(f"   Loaded price data: {close_df.shape} (dates x tickers)")
    else:
        raise FileNotFoundError(f"No price data found in {raw_dir}")

    # Load profile data
    profiles = pd.DataFrame()
    profile_files = list(profile_dir.glob("*.parquet")) if profile_dir.exists() else []

    if profile_files:
        # Load all profile files and combine
        profile_dfs = []
        for profile_file in profile_files:
            df = pd.read_parquet(profile_file, engine='fastparquet')
            profile_dfs.append(df)

        if profile_dfs:
            profiles = pd.concat(profile_dfs, ignore_index=True)
            profiles = profiles.set_index('ticker')
            profiles = profiles[~profiles.index.duplicated()]

            # Filter to requested tickers
            available_profile_tickers = [t for t in tickers if t in profiles.index]
            profiles = profiles.loc[available_profile_tickers]

        print(f"   Loaded profiles: {len(profiles)} companies")
    else:
        print(f"   ⚠️  No profile data found in {profile_dir}")

    return close_df, profiles

def smart_data_loading(tickers: list, lookback: int = 365, force_download: bool = False) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Smart data loading that uses existing data when available.

    Args:
        tickers: List of ticker symbols
        lookback: Number of days of historical data
        force_download: If True, force fresh download

    Returns:
        Tuple of (close_df, profiles)
    """
    print(f"\n📊 Smart data loading for {len(tickers)} tickers...")

    if force_download:
        print("🔄 Force download requested - fetching fresh data...")
        should_download = True
        latest_date = None
    else:
        # Check for existing data
        latest_date = find_latest_data_date()

        if latest_date:
            # Check if data is from today
            today = datetime.now().strftime('%Y_%m_%d')

            if latest_date == today:
                print(f"✅ Found fresh data from today ({latest_date})")
                should_download = False
            else:
                print(f"⚠️  Latest data is from {latest_date} (not today)")
                print(f"   Will download fresh data for today")
                should_download = True
        else:
            print(f"📥 No existing data found - will download fresh data")
            should_download = True

    if should_download:
        # Download fresh data
        print("🔄 Downloading fresh data...")
        dl = DataDownloader(
            lookback=lookback,
            price_freshness_hours=1,  # Force fresh download
            profile_freshness_hours=1
        )

        close_df = dl.fetch_close_df(tickers)
        profiles = dl.fetch_company_profiles(tickers)

        print(f"✅ Downloaded fresh data:")
        print(f"   Price data: {close_df.shape} (dates x tickers)")
        print(f"   Profiles: {len(profiles)} companies")

    else:
        # Load existing data
        try:
            close_df, profiles = load_existing_data(latest_date, tickers)
            print(f"✅ Loaded existing data from {latest_date}")
        except Exception as e:
            print(f"❌ Failed to load existing data: {e}")
            print(f"🔄 Falling back to fresh download...")

            dl = DataDownloader(
                lookback=lookback,
                price_freshness_hours=1,
                profile_freshness_hours=1
            )

            close_df = dl.fetch_close_df(tickers)
            profiles = dl.fetch_company_profiles(tickers)

            print(f"✅ Downloaded fresh data as fallback")

    return close_df, profiles

def main(
    input_csv: str,
    output_path: str = None,
    lookback: int = 365,
    close_horizons=None,
    rs_horizons=None,
    weights=None,
    sma_windows=None,
    force_download: bool = False
):
    """
    Main backend pipeline with smart data loading.

    Args:
        input_csv: Path to CSV file with ticker metadata
        output_path: Optional custom output path
        lookback: Number of days of historical data
        close_horizons: Return calculation horizons
        rs_horizons: RS calculation horizons
        weights: RS weights
        sma_windows: SMA windows
        force_download: If True, force fresh data download
    """
    print("🚀 BACKEND PIPELINE WITH SMART DATA LOADING")
    print("=" * 60)

    # 1) Load metadata
    print(f"📂 Loading ticker metadata from {input_csv}...")
    if not Path(input_csv).exists():
        raise FileNotFoundError(f"Input CSV not found: {input_csv}")

    meta = pd.read_csv(input_csv).set_index("ticker")
    meta = meta[~meta.index.duplicated()]
    print(f"✅ Loaded {len(meta)} unique tickers")

    # 2) Smart data loading
    close_df, profiles = smart_data_loading(
        tickers=meta.index.tolist(),
        lookback=lookback,
        force_download=force_download
    )

    # 3) Compute features with enhanced RS
    print(f"\n⚙️  Computing features for {close_df.shape[1]} tickers with {close_df.shape[0]} days of data...")
    fe = FeatureEngineer(
        close_horizons=close_horizons,
        rs_horizons=rs_horizons,
        weights=weights,
        sma_windows=sma_windows
    )

    # Pass metadata for enhanced RS calculation
    res = fe.compute(close_df, meta)
    snapshot = res.snapshot
    print(f"✅ Feature computation completed. Snapshot shape: {snapshot.shape}")

    # Show RS info if available
    if res.rs_scores is not None:
        print(f"🎯 Enhanced RS scores computed:")
        for rs_type in ['global', 'theme', 'subtheme']:
            if f'rs_{rs_type}' in snapshot.columns:
                rs_stats = snapshot[f'rs_{rs_type}'].describe()
                print(f"   {rs_type.capitalize()} RS: mean={rs_stats['mean']:.3f}, std={rs_stats['std']:.3f}")

        if res.rs_files:
            print(f"💾 RS time series files saved:")
            for rs_type, filepath in res.rs_files.items():
                print(f"   {rs_type}: {filepath.name}")

    # 4) Create dashboard
    print(f"\n🔗 Creating dashboard...")

    # Remove duplicate columns from snapshot (Theme, Subtheme already in meta)
    snapshot_clean = snapshot.drop(columns=['Theme', 'Subtheme'], errors='ignore')

    dashboard = (
        meta
        .join(profiles, how="left")
        .join(snapshot_clean, how="inner")
    )
    print(f"✅ Dashboard created with {dashboard.shape[0]} tickers and {dashboard.shape[1]} columns")

    # 5) Save dashboard
    print(f"\n💾 Saving dashboard...")
    if output_path is None:
        today_processed_dir = get_processed_dir()
        ts = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = today_processed_dir / f"dashboard_{ts}.parquet"
    else:
        output_path = Path(output_path)

    dashboard.to_parquet(output_path, engine="fastparquet")
    print(f"🚀 Dashboard written to: {output_path}")

    # Show summary
    print(f"\n📋 BACKEND PIPELINE SUMMARY")
    print("=" * 40)
    print(f"✅ Processed {len(meta)} tickers")
    print(f"✅ Loaded {close_df.shape[0]} days of price data")
    print(f"✅ Created dashboard with {dashboard.shape[1]} features")

    if 'rs_global' in dashboard.columns:
        print(f"✅ Enhanced RS scores included")
        print(f"\n🏆 Top 3 performers by Global RS:")
        top_3 = dashboard.nlargest(3, 'rs_global')[['Theme', 'Subtheme', 'rs_global']]
        print(top_3.round(4).to_string())

    return dashboard, output_path

if __name__ == "__main__":
    # Default configuration
    print("🚀 Starting Stealth Agency Trading Pipeline...")
    print("=" * 60)

    # You can customize these parameters as needed
    config = {
        "input_csv": "default_tickers.csv",
        "lookback": 365,
        "close_horizons": ['return_1d', 'return_20d', 'return_22d', 'return_200d'],
        "rs_horizons": ['return_20d', 'return_22d', 'return_200d'],
        "weights": None,  # Will use default equal weights
        "sma_windows": [5, 10, 20, 120]
    }

    print(f"📋 Configuration:")
    print(f"   Input CSV: {config['input_csv']}")
    print(f"   Lookback days: {config['lookback']}")
    print(f"   Close horizons: {config['close_horizons']}")
    print(f"   RS horizons: {config['rs_horizons']}")
    print(f"   SMA windows: {config['sma_windows']}")
    print()

    try:
        # Run the main pipeline with smart data loading
        dashboard, output_path = main(
            input_csv=config["input_csv"],
            lookback=config["lookback"],
            close_horizons=config["close_horizons"],
            rs_horizons=config["rs_horizons"],
            weights=config["weights"],
            sma_windows=config["sma_windows"],
            force_download=False  # Use existing data when available
        )

        print(f"\n📊 Final Results:")
        print(f"   Dashboard shape: {dashboard.shape}")
        print(f"   Output file: {output_path}")

        print("\n" + "=" * 60)
        print("🎉 Pipeline completed successfully!")
        print("📁 Check the data/processed/yyyy_mm_dd/ directory for output files")
        print("📊 Check the logs/ directory for detailed logs")

    except Exception as e:
        print(f"\n❌ Pipeline failed with error: {e}")
        print("📋 Check the logs for more details")
        raise