#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced downloader with logging and progress bars.
"""

import pandas as pd
from downloader import DataDownloader

def test_small_download():
    """Test the enhanced downloader with a small set of tickers."""
    print("=== Testing Enhanced Downloader ===\n")
    
    # Test with a small subset of tickers
    test_tickers = ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'TSLA']
    
    print(f"Testing with {len(test_tickers)} tickers: {', '.join(test_tickers)}")
    
    # Initialize downloader with short TTL to force fresh downloads
    dl = DataDownloader(
        lookback=30,  # Shorter lookback for faster testing
        price_freshness_hours=0,  # Force fresh downloads
        profile_freshness_hours=0,  # Force fresh downloads
        enable_logging=True
    )
    
    try:
        # Test OHLC download
        print("\n" + "="*50)
        print("TESTING OHLC DOWNLOADS")
        print("="*50)
        
        close_df = dl.fetch_close_df(test_tickers)
        print(f"\n📈 OHLC Results:")
        print(f"   Shape: {close_df.shape}")
        print(f"   Columns: {list(close_df.columns)}")
        print(f"   Date range: {close_df.index.min()} to {close_df.index.max()}")
        
        # Test profile download
        print("\n" + "="*50)
        print("TESTING PROFILE DOWNLOADS")
        print("="*50)
        
        profiles = dl.fetch_company_profiles(test_tickers)
        print(f"\n🏢 Profile Results:")
        print(f"   Shape: {profiles.shape}")
        print(f"   Columns: {list(profiles.columns)}")
        if not profiles.empty:
            print(f"   Sample companies: {profiles['companyName'].head().tolist()}")
        
        print(f"\n🎉 Test completed successfully!")
        print(f"📁 Check the logs directory for detailed logs")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        raise

def test_cached_download():
    """Test that cached data is properly used."""
    print("\n" + "="*50)
    print("TESTING CACHED DATA USAGE")
    print("="*50)
    
    test_tickers = ['AAPL', 'MSFT']
    
    # First download with normal TTL
    dl = DataDownloader(
        lookback=30,
        price_freshness_hours=24,
        profile_freshness_hours=24,
        enable_logging=True
    )
    
    print("First download (should fetch from API if not cached):")
    close_df1 = dl.fetch_close_df(test_tickers)
    profiles1 = dl.fetch_company_profiles(test_tickers)
    
    print("\nSecond download (should use cached data):")
    close_df2 = dl.fetch_close_df(test_tickers)
    profiles2 = dl.fetch_company_profiles(test_tickers)
    
    print(f"\n✅ Cache test completed!")
    print(f"   OHLC data identical: {close_df1.equals(close_df2)}")
    print(f"   Profile data identical: {profiles1.equals(profiles2)}")

if __name__ == "__main__":
    # Run the tests
    test_small_download()
    test_cached_download()
