import pathlib
import pandas as pd
from typing import Optional
from project_settings.config import (
    get_most_recent_date_dir, get_processed_dir,
    PROCESSED_BASE_DIR, RAW_BASE_DIR, PROFILE_BASE_DIR
)

def load_latest_snapshot(processed_dir: Optional[pathlib.Path] = None) -> pd.DataFrame:
    """Load the most recent features snapshot from date-based directories."""
    if processed_dir is None:
        # Find the most recent date directory
        recent_dir = get_most_recent_date_dir(PROCESSED_BASE_DIR)
        if recent_dir is None:
            raise FileNotFoundError("No processed data directories found")
        processed_dir = recent_dir

    files = list(processed_dir.glob('features_*.parquet'))
    if not files:
        raise FileNotFoundError(f"No feature files found in {processed_dir}")

    latest = max(files, key=lambda p: p.stat().st_mtime)
    return pd.read_parquet(latest, engine='fastparquet')

def load_latest_timeseries(processed_dir: Optional[pathlib.Path] = None) -> pd.DataFrame:
    """Load the most recent timeseries data from date-based directories."""
    if processed_dir is None:
        # Find the most recent date directory
        recent_dir = get_most_recent_date_dir(PROCESSED_BASE_DIR)
        if recent_dir is None:
            raise FileNotFoundError("No processed data directories found")
        processed_dir = recent_dir

    files = list(processed_dir.glob('rs_timeseries_*.parquet'))
    if not files:
        raise FileNotFoundError(f"No timeseries files found in {processed_dir}")

    latest = max(files, key=lambda p: p.stat().st_mtime)
    return pd.read_parquet(latest, engine='fastparquet')

def load_latest_dashboard() -> pd.DataFrame:
    """Load the most recent dashboard data from date-based directories."""
    recent_dir = get_most_recent_date_dir(PROCESSED_BASE_DIR)
    if recent_dir is None:
        raise FileNotFoundError("No processed data directories found")

    files = list(recent_dir.glob('dashboard_*.parquet'))
    if not files:
        raise FileNotFoundError(f"No dashboard files found in {recent_dir}")

    latest = max(files, key=lambda p: p.stat().st_mtime)
    return pd.read_parquet(latest, engine='fastparquet')

def get_available_dates(data_type: str = 'processed') -> list:
    """Get list of available dates for a data type."""
    if data_type == 'processed':
        base_dir = PROCESSED_BASE_DIR
    elif data_type == 'raw':
        base_dir = RAW_BASE_DIR
    elif data_type == 'profiles':
        base_dir = PROFILE_BASE_DIR
    else:
        raise ValueError("data_type must be 'processed', 'raw', or 'profiles'")

    if not base_dir.exists():
        return []

    date_dirs = [d.name for d in base_dir.iterdir()
                 if d.is_dir() and len(d.name) == 10 and d.name.count('_') == 2]
    return sorted(date_dirs)

def load_data_for_date(date_str: str, data_type: str = 'processed', file_pattern: str = 'features_*.parquet') -> pd.DataFrame:
    """Load data for a specific date."""
    if data_type == 'processed':
        base_dir = PROCESSED_BASE_DIR
    elif data_type == 'raw':
        base_dir = RAW_BASE_DIR
    elif data_type == 'profiles':
        base_dir = PROFILE_BASE_DIR
    else:
        raise ValueError("data_type must be 'processed', 'raw', or 'profiles'")

    date_dir = base_dir / date_str
    if not date_dir.exists():
        raise FileNotFoundError(f"No data directory found for date {date_str}")

    files = list(date_dir.glob(file_pattern))
    if not files:
        raise FileNotFoundError(f"No files matching {file_pattern} found in {date_dir}")

    latest = max(files, key=lambda p: p.stat().st_mtime)
    return pd.read_parquet(latest, engine='fastparquet')