#!/usr/bin/env python3
"""
Enhanced Relative Strength Calculator with theme and subtheme rankings.
Provides global, theme-based, and subtheme-based RS scores with time series tracking.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
from project_settings.config import get_processed_dir

class RelativeStrengthCalculator:
    """
    Enhanced relative strength calculator that computes RS scores at multiple levels:
    1. Global RS Score (across all tickers)
    2. Theme RS Score (within each theme)
    3. Subtheme RS Score (within each subtheme)
    
    Also tracks time series for all RS scores.
    """
    
    def __init__(
        self,
        rs_horizons: List[str] = None,
        weights: Dict[str, float] = None
    ):
        self.rs_horizons = rs_horizons or ['return_20d', 'return_22d', 'return_200d']
        default_w = {h: 1/len(self.rs_horizons) for h in self.rs_horizons}
        self.weights = weights or default_w
        
    def compute_returns_ranks(self, returns: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Compute cross-sectional ranks for returns (global ranking)."""
        return {
            h: returns[h].rank(axis=1, pct=True).fillna(0)
            for h in self.rs_horizons
        }
    
    def compute_theme_ranks(
        self, 
        returns: Dict[str, pd.DataFrame], 
        meta: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """Compute ranks within each theme."""
        theme_ranks = {}
        
        for horizon in self.rs_horizons:
            returns_df = returns[horizon]
            theme_rank_df = pd.DataFrame(index=returns_df.index, columns=returns_df.columns)
            
            for date in returns_df.index:
                date_returns = returns_df.loc[date].dropna()
                
                # Group by theme and rank within each theme
                for theme in meta['Theme'].unique():
                    theme_tickers = meta[meta['Theme'] == theme].index
                    theme_tickers_available = [t for t in theme_tickers if t in date_returns.index]
                    
                    if len(theme_tickers_available) > 1:
                        theme_returns = date_returns[theme_tickers_available]
                        theme_ranks_pct = theme_returns.rank(pct=True)
                        theme_rank_df.loc[date, theme_tickers_available] = theme_ranks_pct
                    elif len(theme_tickers_available) == 1:
                        # Single ticker in theme gets rank of 0.5
                        theme_rank_df.loc[date, theme_tickers_available[0]] = 0.5
            
            theme_ranks[horizon] = theme_rank_df.fillna(0)
        
        return theme_ranks
    
    def compute_subtheme_ranks(
        self, 
        returns: Dict[str, pd.DataFrame], 
        meta: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """Compute ranks within each subtheme."""
        subtheme_ranks = {}
        
        for horizon in self.rs_horizons:
            returns_df = returns[horizon]
            subtheme_rank_df = pd.DataFrame(index=returns_df.index, columns=returns_df.columns)
            
            for date in returns_df.index:
                date_returns = returns_df.loc[date].dropna()
                
                # Group by subtheme and rank within each subtheme
                for subtheme in meta['Subtheme'].unique():
                    subtheme_tickers = meta[meta['Subtheme'] == subtheme].index
                    subtheme_tickers_available = [t for t in subtheme_tickers if t in date_returns.index]
                    
                    if len(subtheme_tickers_available) > 1:
                        subtheme_returns = date_returns[subtheme_tickers_available]
                        subtheme_ranks_pct = subtheme_returns.rank(pct=True)
                        subtheme_rank_df.loc[date, subtheme_tickers_available] = subtheme_ranks_pct
                    elif len(subtheme_tickers_available) == 1:
                        # Single ticker in subtheme gets rank of 0.5
                        subtheme_rank_df.loc[date, subtheme_tickers_available[0]] = 0.5
            
            subtheme_ranks[horizon] = subtheme_rank_df.fillna(0)
        
        return subtheme_ranks
    
    def compute_rs_score(self, ranks: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Compute weighted RS score from ranks."""
        total = sum(self.weights.values())
        rs = sum(ranks[h] * (self.weights[h]/total) for h in self.rs_horizons)
        rs.index.name = 'date'
        return rs
    
    def compute_all_rs_scores(
        self, 
        returns: Dict[str, pd.DataFrame], 
        meta: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """
        Compute all RS scores: global, theme-based, and subtheme-based.
        
        Returns:
            Dictionary with keys: 'global', 'theme', 'subtheme'
        """
        print("🔄 Computing global RS scores...")
        global_ranks = self.compute_returns_ranks(returns)
        global_rs = self.compute_rs_score(global_ranks)
        
        print("🎯 Computing theme-based RS scores...")
        theme_ranks = self.compute_theme_ranks(returns, meta)
        theme_rs = self.compute_rs_score(theme_ranks)
        
        print("🎯 Computing subtheme-based RS scores...")
        subtheme_ranks = self.compute_subtheme_ranks(returns, meta)
        subtheme_rs = self.compute_rs_score(subtheme_ranks)
        
        return {
            'global': global_rs,
            'theme': theme_rs,
            'subtheme': subtheme_rs,
            'global_ranks': global_ranks,
            'theme_ranks': theme_ranks,
            'subtheme_ranks': subtheme_ranks
        }
    
    def create_rs_snapshot(
        self, 
        rs_scores: Dict[str, pd.DataFrame], 
        meta: pd.DataFrame
    ) -> pd.DataFrame:
        """Create snapshot with all RS scores for the latest date."""
        latest_date = rs_scores['global'].index.max()
        
        snapshot = pd.DataFrame(index=rs_scores['global'].columns)
        snapshot.index.name = 'ticker'
        
        # Add RS scores
        snapshot['rs_global'] = rs_scores['global'].loc[latest_date]
        snapshot['rs_theme'] = rs_scores['theme'].loc[latest_date]
        snapshot['rs_subtheme'] = rs_scores['subtheme'].loc[latest_date]
        
        # Add metadata
        snapshot = snapshot.join(meta[['Theme', 'Subtheme']], how='left')
        
        # Add percentile ranks for easier interpretation
        snapshot['rs_global_pct'] = snapshot['rs_global'].rank(pct=True)
        
        # Theme percentiles (within each theme)
        for theme in meta['Theme'].unique():
            theme_mask = snapshot['Theme'] == theme
            if theme_mask.sum() > 1:
                snapshot.loc[theme_mask, 'rs_theme_pct'] = snapshot.loc[theme_mask, 'rs_theme'].rank(pct=True)
            else:
                snapshot.loc[theme_mask, 'rs_theme_pct'] = 0.5
        
        # Subtheme percentiles (within each subtheme)
        for subtheme in meta['Subtheme'].unique():
            subtheme_mask = snapshot['Subtheme'] == subtheme
            if subtheme_mask.sum() > 1:
                snapshot.loc[subtheme_mask, 'rs_subtheme_pct'] = snapshot.loc[subtheme_mask, 'rs_subtheme'].rank(pct=True)
            else:
                snapshot.loc[subtheme_mask, 'rs_subtheme_pct'] = 0.5
        
        snapshot['data_date'] = latest_date
        
        return snapshot
    
    def save_rs_timeseries(
        self, 
        rs_scores: Dict[str, pd.DataFrame], 
        output_dir: Optional[Path] = None
    ) -> Dict[str, Path]:
        """Save RS time series to parquet files."""
        if output_dir is None:
            output_dir = get_processed_dir()
        
        output_dir.mkdir(parents=True, exist_ok=True)
        ts = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        files = {}
        for score_type in ['global', 'theme', 'subtheme']:
            filename = f'rs_{score_type}_timeseries_{ts}.parquet'
            filepath = output_dir / filename
            rs_scores[score_type].to_parquet(filepath, engine='fastparquet')
            files[score_type] = filepath
            print(f"💾 Saved {score_type} RS timeseries to {filepath}")
        
        return files
