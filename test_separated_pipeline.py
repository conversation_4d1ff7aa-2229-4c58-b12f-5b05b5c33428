#!/usr/bin/env python3
"""
Test the separated and smart pipeline functionality.
"""

import pandas as pd
from pathlib import Path
import sys

def create_test_csv():
    """Create a test CSV file."""
    test_data = {
        'ticker': ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'TSLA'],
        'Theme': ['Tech', 'Tech', 'Tech', 'Tech', 'Auto'],
        'Subtheme': ['Hardware', 'Software', 'Search', 'AI', 'EV']
    }
    
    df = pd.DataFrame(test_data)
    df.to_csv('test_separated_pipeline.csv', index=False)
    return 'test_separated_pipeline.csv'

def test_separated_pipeline():
    """Test the separated pipeline components."""
    print("🧪 TESTING SEPARATED PIPELINE COMPONENTS")
    print("=" * 60)
    
    # Create test CSV
    csv_file = create_test_csv()
    
    try:
        # Test 1: Test individual components
        print("\n📊 TEST 1: Testing individual components")
        print("-" * 40)
        
        from main import load_metadata, download_data, compute_features, load_existing_rs_data
        
        # Test metadata loading
        print("🔍 Testing metadata loading...")
        meta = load_metadata(csv_file)
        print(f"✅ Metadata loaded: {len(meta)} tickers")
        
        # Test data download (with cached data preference)
        print("\n🔍 Testing data download...")
        close_df, profiles = download_data(
            tickers=meta.index.tolist(),
            lookback=30,  # Short for testing
            force_download=False  # Use cached if available
        )
        print(f"✅ Data loaded: {close_df.shape} prices, {len(profiles)} profiles")
        
        # Test existing RS data loading
        print("\n🔍 Testing existing RS data loading...")
        existing_rs_info = load_existing_rs_data()
        if existing_rs_info:
            print(f"✅ Found existing RS data from {existing_rs_info['date']}")
        else:
            print("ℹ️  No existing RS data found")
        
        # Test feature computation
        print("\n🔍 Testing feature computation...")
        snapshot, rs_files_info = compute_features(
            close_df=close_df,
            meta=meta,
            close_horizons=['return_1d', 'return_5d'],
            rs_horizons=['return_5d'],
            weights={'return_5d': 1.0},
            sma_windows=[5]
        )
        print(f"✅ Features computed: {snapshot.shape}")
        
        # Test 2: Test full pipeline with smart loading
        print("\n📊 TEST 2: Testing full pipeline with smart loading")
        print("-" * 40)
        
        from main import main
        
        # First run - should compute everything fresh
        print("🔄 First run (fresh computation)...")
        dashboard1, output_path1 = main(
            input_csv=csv_file,
            lookback=30,
            close_horizons=['return_1d', 'return_5d'],
            rs_horizons=['return_5d'],
            weights={'return_5d': 1.0},
            sma_windows=[5],
            force_download=False,
            use_existing_rs=False,  # Force fresh RS computation
            rs_date=None
        )
        
        print(f"✅ First run completed:")
        print(f"   Dashboard shape: {dashboard1.shape}")
        print(f"   Output: {output_path1}")
        
        # Second run - should use existing RS data
        print("\n🔄 Second run (smart loading)...")
        dashboard2, output_path2 = main(
            input_csv=csv_file,
            lookback=30,
            close_horizons=['return_1d', 'return_5d'],
            rs_horizons=['return_5d'],
            weights={'return_5d': 1.0},
            sma_windows=[5],
            force_download=False,
            use_existing_rs=True,  # Use existing RS data
            rs_date=None
        )
        
        print(f"✅ Second run completed:")
        print(f"   Dashboard shape: {dashboard2.shape}")
        print(f"   Output: {output_path2}")
        
        # Test 3: Compare results
        print("\n📊 TEST 3: Comparing results")
        print("-" * 40)
        
        # Check if RS columns are similar (should be identical or very close)
        rs_columns = [col for col in dashboard1.columns if col.startswith('rs_')]
        print(f"RS columns: {rs_columns}")
        
        if rs_columns:
            for col in rs_columns:
                if col in dashboard2.columns:
                    diff = (dashboard1[col] - dashboard2[col]).abs().max()
                    print(f"   {col}: max difference = {diff:.6f}")
                    if diff < 0.001:
                        print(f"     ✅ Results are consistent")
                    else:
                        print(f"     ⚠️  Results differ (expected for fresh vs cached)")
        
        # Test 4: Test force download
        print("\n📊 TEST 4: Testing force download")
        print("-" * 40)
        
        dashboard3, output_path3 = main(
            input_csv=csv_file,
            lookback=30,
            close_horizons=['return_1d', 'return_5d'],
            rs_horizons=['return_5d'],
            weights={'return_5d': 1.0},
            sma_windows=[5],
            force_download=True,  # Force fresh download
            use_existing_rs=True,
            rs_date=None
        )
        
        print(f"✅ Force download completed:")
        print(f"   Dashboard shape: {dashboard3.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test file
        if Path(csv_file).exists():
            Path(csv_file).unlink()
            print(f"🧹 Cleaned up {csv_file}")

if __name__ == "__main__":
    try:
        success = test_separated_pipeline()
        if success:
            print("\n🎉 Separated pipeline test completed successfully!")
            print("\n📋 Key Features Demonstrated:")
            print("✅ Separated download and feature engineering")
            print("✅ Smart data loading (cached vs fresh)")
            print("✅ Existing RS data reuse")
            print("✅ Force download option")
            print("✅ Modular pipeline components")
        else:
            print("\n❌ Separated pipeline test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
