#!/usr/bin/env python3
"""
Example script demonstrating the enhanced relative strength calculation
with global, theme, and subtheme RS scores.
"""

import pandas as pd
import numpy as np
from backend.downloader import DataDownloader
from backend.feature_engineer import FeatureEngineer
from relative_strength_calculator import RelativeStrengthCalculator
from project_settings.config import get_processed_dir

def demonstrate_rs_calculation():
    """Demonstrate the enhanced RS calculation with examples."""
    print("🚀 Enhanced Relative Strength Calculation Demo")
    print("=" * 60)
    
    # 1. Load sample data
    print("📂 Loading sample ticker data...")
    meta = pd.read_csv("default_tickers.csv").set_index("ticker")
    meta = meta[~meta.index.duplicated()]
    
    # Use a smaller subset for demo
    sample_tickers = meta.head(20).index.tolist()
    meta_sample = meta.loc[sample_tickers]
    
    print(f"✅ Loaded {len(meta_sample)} tickers for demo")
    print(f"📊 Themes: {meta_sample['Theme'].unique()}")
    print(f"📊 Subthemes: {meta_sample['Subtheme'].nunique()} unique subthemes")
    
    # 2. Download price data
    print(f"\n📈 Downloading price data...")
    dl = DataDownloader(lookback=90, price_freshness_hours=24)
    close_df = dl.fetch_close_df(sample_tickers)
    print(f"✅ Downloaded data for {close_df.shape[1]} tickers, {close_df.shape[0]} days")
    
    # 3. Compute returns
    print(f"\n⚙️  Computing returns...")
    fe = FeatureEngineer()
    returns = fe._compute_returns(close_df)
    print(f"✅ Computed returns for horizons: {list(returns.keys())}")
    
    # 4. Initialize RS Calculator
    print(f"\n🎯 Initializing Enhanced RS Calculator...")
    rs_calc = RelativeStrengthCalculator(
        rs_horizons=['return_20d', 'return_22d'],
        weights={'return_20d': 0.6, 'return_22d': 0.4}
    )
    
    # 5. Compute all RS scores
    print(f"\n🔄 Computing all RS scores...")
    rs_scores = rs_calc.compute_all_rs_scores(returns, meta_sample)
    
    # 6. Create snapshot
    print(f"\n📸 Creating RS snapshot...")
    rs_snapshot = rs_calc.create_rs_snapshot(rs_scores, meta_sample)
    
    # 7. Save time series
    print(f"\n💾 Saving RS time series...")
    saved_files = rs_calc.save_rs_timeseries(rs_scores)
    
    # 8. Display results
    print(f"\n📊 RESULTS SUMMARY")
    print("=" * 60)
    
    print(f"\n🌍 TOP 5 GLOBAL RS SCORES:")
    top_global = rs_snapshot.nlargest(5, 'rs_global')[
        ['Theme', 'Subtheme', 'rs_global', 'rs_global_pct']
    ]
    print(top_global.round(4))
    
    print(f"\n🎯 THEME ANALYSIS:")
    theme_summary = rs_snapshot.groupby('Theme').agg({
        'rs_global': ['mean', 'std', 'count'],
        'rs_theme': ['mean', 'std'],
        'rs_subtheme': ['mean', 'std']
    }).round(4)
    print(theme_summary)
    
    print(f"\n🏆 TOP PERFORMERS BY THEME:")
    for theme in meta_sample['Theme'].unique():
        theme_data = rs_snapshot[rs_snapshot['Theme'] == theme]
        if len(theme_data) > 0:
            top_in_theme = theme_data.nlargest(2, 'rs_theme')[
                ['Subtheme', 'rs_global', 'rs_theme', 'rs_subtheme']
            ]
            print(f"\n{theme}:")
            print(top_in_theme.round(4))
    
    return rs_snapshot, rs_scores, saved_files

def analyze_rs_timeseries(rs_scores: dict, meta: pd.DataFrame):
    """Analyze RS time series patterns."""
    print(f"\n📈 TIME SERIES ANALYSIS")
    print("=" * 60)
    
    # Get latest 10 days of data
    global_rs = rs_scores['global']
    recent_data = global_rs.tail(10)
    
    print(f"\n📊 RS Score Changes (Last 10 Days):")
    print(f"Date range: {recent_data.index.min()} to {recent_data.index.max()}")
    
    # Calculate daily changes
    rs_changes = recent_data.diff().iloc[-1]  # Last day's change
    
    # Top gainers and losers
    print(f"\n🚀 BIGGEST RS GAINERS (Last Day):")
    top_gainers = rs_changes.nlargest(3)
    for ticker, change in top_gainers.items():
        theme = meta.loc[ticker, 'Theme'] if ticker in meta.index else 'Unknown'
        print(f"   {ticker} ({theme}): +{change:.4f}")
    
    print(f"\n📉 BIGGEST RS DECLINERS (Last Day):")
    top_decliners = rs_changes.nsmallest(3)
    for ticker, change in top_decliners.items():
        theme = meta.loc[ticker, 'Theme'] if ticker in meta.index else 'Unknown'
        print(f"   {ticker} ({theme}): {change:.4f}")
    
    # Volatility analysis
    print(f"\n📊 RS VOLATILITY (10-day std):")
    rs_volatility = recent_data.std().nlargest(3)
    for ticker, vol in rs_volatility.items():
        theme = meta.loc[ticker, 'Theme'] if ticker in meta.index else 'Unknown'
        print(f"   {ticker} ({theme}): {vol:.4f}")

def compare_rs_levels(rs_snapshot: pd.DataFrame):
    """Compare different levels of RS scores."""
    print(f"\n🔍 RS SCORE LEVEL COMPARISON")
    print("=" * 60)
    
    # Correlation between different RS levels
    correlations = rs_snapshot[['rs_global', 'rs_theme', 'rs_subtheme']].corr()
    print(f"\n📊 Correlations between RS levels:")
    print(correlations.round(4))
    
    # Find tickers with divergent scores
    print(f"\n🎯 DIVERGENT SCORES (High theme RS, Low global RS):")
    divergent = rs_snapshot[
        (rs_snapshot['rs_theme_pct'] > 0.7) & 
        (rs_snapshot['rs_global_pct'] < 0.3)
    ][['Theme', 'Subtheme', 'rs_global_pct', 'rs_theme_pct', 'rs_subtheme_pct']]
    
    if len(divergent) > 0:
        print(divergent.round(4))
    else:
        print("   No significant divergences found in this sample")
    
    # Theme strength analysis
    print(f"\n🏆 THEME STRENGTH RANKING:")
    theme_strength = rs_snapshot.groupby('Theme')['rs_global'].mean().sort_values(ascending=False)
    for i, (theme, avg_rs) in enumerate(theme_strength.items(), 1):
        count = (rs_snapshot['Theme'] == theme).sum()
        print(f"   {i}. {theme}: {avg_rs:.4f} (n={count})")

if __name__ == "__main__":
    try:
        # Run the demonstration
        rs_snapshot, rs_scores, saved_files = demonstrate_rs_calculation()
        
        # Additional analysis
        meta = pd.read_csv("default_tickers.csv").set_index("ticker")
        meta = meta[~meta.index.duplicated()]
        
        analyze_rs_timeseries(rs_scores, meta)
        compare_rs_levels(rs_snapshot)
        
        print(f"\n🎉 Demo completed successfully!")
        print(f"📁 Files saved:")
        for score_type, filepath in saved_files.items():
            print(f"   {score_type}: {filepath}")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        raise
