# downloader.py

import time
import pandas as pd
import requests
import logging
from typing import List, Optional
from datetime import datetime
from tqdm import tqdm
from project_settings.config import (
    get_raw_dir, get_profile_dir, get_most_recent_date_dir,
    RAW_BASE_DIR, PROFILE_BASE_DIR, FMP_KEY, LOG_DIR
)

# Setup logging
def setup_logging():
    """Setup logging for the downloader."""
    log_file = LOG_DIR / f"downloader_{datetime.now().strftime('%Y_%m_%d')}.log"

    # Create logger
    logger = logging.getLogger('downloader')
    logger.setLevel(logging.INFO)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create file handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

class DataDownloader:
    """Downloads and caches both OHLC data and company profiles for tickers."""

    def __init__(
        self,
        lookback: int = 365,
        price_freshness_hours: int = 24,
        profile_freshness_hours: int = 24
    ):
        self.lookback = lookback
        self.price_ttl = price_freshness_hours * 3600
        self.profile_ttl = profile_freshness_hours * 3600

    def fetch_ohlc(self, ticker: str) -> pd.DataFrame:
        # Check for cached data in today's directory first
        today_raw_dir = get_raw_dir()
        cache = today_raw_dir / f"{ticker}.parquet"

        # If today's cache exists and is fresh, use it
        if cache.exists() and (time.time() - cache.stat().st_mtime) < self.price_ttl:
            return pd.read_parquet(cache, engine="fastparquet")

        # Check recent date directories for cached data
        recent_dir = get_most_recent_date_dir(RAW_BASE_DIR)
        if recent_dir and recent_dir != today_raw_dir:
            recent_cache = recent_dir / f"{ticker}.parquet"
            if recent_cache.exists() and (time.time() - recent_cache.stat().st_mtime) < self.price_ttl:
                return pd.read_parquet(recent_cache, engine="fastparquet")

        # Fetch from API
        url = (
            f"https://financialmodelingprep.com/api/v3/historical-price-full/{ticker}?"
            f"timeseries={self.lookback}&apikey={FMP_KEY}"
        )
        r = requests.get(url)
        r.raise_for_status()
        raw = r.json().get("historical", [])

        # Build DataFrame
        df = pd.DataFrame(raw)
        # Double-check DataFrame
        if df.empty:
            msg = f"Parsed DataFrame for ticker '{ticker}' is empty after formatting"
            print(msg)
            raise ValueError(msg)
        df["date"] = pd.to_datetime(df["date"])
        df = df.set_index("date").sort_index()

        # Check for empty data and raise exception
        if df.empty:
            msg = f"No OHLC data found for ticker '{ticker}' over the last {self.lookback} days"
            print(msg)
            raise ValueError(msg)

        # Cache in today's directory and return
        today_raw_dir.mkdir(parents=True, exist_ok=True)
        df.to_parquet(cache, engine="fastparquet")
        return df

    def fetch_close_df(self, tickers: List[str]) -> pd.DataFrame:
        frames = []
        for t in tickers:
            try:
                df = self.fetch_ohlc(t)
            except ValueError as e:
                print(f"Skipping {t}: {e}")
                continue
        # for t in tickers:
        #     print(t)
        #     if t == "LILMF":
        #         print("LILMF")
        #     df = self.fetch_ohlc(t)
            frames.append(df["close"].rename(t))
        return pd.concat(frames, axis=1)

    def fetch_company_profiles(self, tickers: List[str]) -> pd.DataFrame:
        """
        Caches each ticker's profile in date-based profile directories
        with columns: companyName, beta, marketCap, profile_date.
        """
        recs = []
        today_profile_dir = get_profile_dir()

        for t in tickers:
            # Check today's directory first
            cache = today_profile_dir / f"{t}.parquet"

            if cache.exists() and (time.time() - cache.stat().st_mtime) < self.profile_ttl:
                prof = pd.read_parquet(cache, engine="fastparquet")
            else:
                # Check recent date directories for cached data
                recent_dir = get_most_recent_date_dir(PROFILE_BASE_DIR)
                if recent_dir and recent_dir != today_profile_dir:
                    recent_cache = recent_dir / f"{t}.parquet"
                    if recent_cache.exists() and (time.time() - recent_cache.stat().st_mtime) < self.profile_ttl:
                        prof = pd.read_parquet(recent_cache, engine="fastparquet")
                        recs.append(prof)
                        continue

                # Fetch from API
                url = f"https://financialmodelingprep.com/api/v3/profile/{t}?apikey={FMP_KEY}"
                r = requests.get(url)
                r.raise_for_status()
                data = r.json()
                if not data:
                    continue
                d = data[0]
                prof = pd.DataFrame([{
                    "companyName": d.get("companyName"),
                    "beta": d.get("beta"),
                    "marketCap": d.get("mktCap"),
                    "profile_date": pd.Timestamp.now()
                }], index=[t])

                # Save to today's directory
                today_profile_dir.mkdir(parents=True, exist_ok=True)
                prof.to_parquet(cache, engine="fastparquet")

            recs.append(prof)

        if recs:
            return pd.concat(recs)
        return pd.DataFrame(columns=["companyName","beta","marketCap","profile_date"])
