#!/usr/bin/env python3
"""
Example script demonstrating how to use the new date-based data loading functionality.
"""

import pandas as pd
from utils import (
    load_latest_snapshot, 
    load_latest_timeseries, 
    load_latest_dashboard,
    get_available_dates,
    load_data_for_date
)
from project_settings.config import get_processed_dir, get_raw_dir, get_profile_dir

def main():
    print("=== Date-based Data Loading Examples ===\n")
    
    # 1. Show available dates
    print("1. Available dates:")
    try:
        processed_dates = get_available_dates('processed')
        raw_dates = get_available_dates('raw')
        profile_dates = get_available_dates('profiles')
        
        print(f"   Processed data: {processed_dates}")
        print(f"   Raw data: {raw_dates}")
        print(f"   Profile data: {profile_dates}")
    except Exception as e:
        print(f"   Error getting dates: {e}")
    
    print()
    
    # 2. Load most recent data
    print("2. Loading most recent data:")
    try:
        # Load latest snapshot
        snapshot = load_latest_snapshot()
        print(f"   Latest snapshot shape: {snapshot.shape}")
        print(f"   Latest snapshot columns: {list(snapshot.columns)}")
        
        # Load latest timeseries
        timeseries = load_latest_timeseries()
        print(f"   Latest timeseries shape: {timeseries.shape}")
        
        # Load latest dashboard
        dashboard = load_latest_dashboard()
        print(f"   Latest dashboard shape: {dashboard.shape}")
        
    except FileNotFoundError as e:
        print(f"   No data found: {e}")
    except Exception as e:
        print(f"   Error loading data: {e}")
    
    print()
    
    # 3. Show today's directory paths
    print("3. Today's directory paths:")
    print(f"   Raw: {get_raw_dir()}")
    print(f"   Profiles: {get_profile_dir()}")
    print(f"   Processed: {get_processed_dir()}")
    
    print()
    
    # 4. Load data for specific date (if available)
    print("4. Loading data for specific date:")
    try:
        dates = get_available_dates('processed')
        if dates:
            latest_date = dates[-1]
            print(f"   Loading data for date: {latest_date}")
            
            snapshot = load_data_for_date(latest_date, 'processed', 'features_*.parquet')
            print(f"   Snapshot for {latest_date} shape: {snapshot.shape}")
            
            timeseries = load_data_for_date(latest_date, 'processed', 'rs_timeseries_*.parquet')
            print(f"   Timeseries for {latest_date} shape: {timeseries.shape}")
        else:
            print("   No processed data dates available")
    except Exception as e:
        print(f"   Error: {e}")

if __name__ == "__main__":
    main()
