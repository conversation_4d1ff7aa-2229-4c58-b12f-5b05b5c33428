#!/usr/bin/env python3
"""
Main entry point for the Stealth Agency Trading Pipeline.
This script runs the complete pipeline from data download to dashboard generation.
"""

import pandas as pd
from pathlib import Path
from backend.downloader import DataDownloader
from backend.feature_engineer import FeatureEngineer
from project_settings.config import get_processed_dir

def main(
    input_csv: str,
    output_path: str = None,
    lookback: int = 365,
    close_horizons=None,
    rs_horizons=None,
    weights=None,
    sma_windows=None
):
    """
    Main pipeline function that orchestrates the entire trading data processing workflow.
    
    Args:
        input_csv: Path to CSV file with ticker metadata (Theme, Subtheme, ticker columns)
        output_path: Optional custom output path for dashboard file
        lookback: Number of days of historical data to fetch
        close_horizons: List of return calculation horizons
        rs_horizons: Subset of close_horizons used for RS score calculation
        weights: Custom weights for RS score calculation
        sma_windows: List of SMA window sizes
    """
    # 1) Load your base CSV (must include columns: Theme, Subtheme, ticker)
    print(f"📂 Loading ticker metadata from {input_csv}...")
    meta = pd.read_csv(input_csv).set_index("ticker")
    meta = meta[~meta.index.duplicated()]
    print(f"✅ Loaded {len(meta)} unique tickers")

    # 2) Download prices + profiles
    print(f"\n🔄 Initializing DataDownloader...")
    dl = DataDownloader(
        lookback=lookback,
        price_freshness_hours=24,
        profile_freshness_hours=24
    )
    
    print(f"📊 Processing {len(meta)} unique tickers...")
    close_df = dl.fetch_close_df(meta.index.tolist())
    profiles = dl.fetch_company_profiles(meta.index.tolist())

    # 3) Compute features
    print(f"\n⚙️  Computing features for {close_df.shape[1]} tickers with {close_df.shape[0]} days of data...")
    fe = FeatureEngineer(
        close_horizons=close_horizons,
        rs_horizons=rs_horizons,
        weights=weights,
        sma_windows=sma_windows
    )
    res = fe.compute(close_df, meta)  # Pass metadata for enhanced RS calculation
    snapshot = res.snapshot  # has 'data_date'
    print(f"✅ Feature computation completed. Snapshot shape: {snapshot.shape}")

    # Show enhanced RS info if available
    if res.rs_scores is not None:
        print(f"🎯 Enhanced RS scores computed:")
        print(f"   - Global RS: {snapshot['rs_global'].describe().round(4)}")
        print(f"   - Theme RS: {snapshot['rs_theme'].describe().round(4)}")
        print(f"   - Subtheme RS: {snapshot['rs_subtheme'].describe().round(4)}")

        # Show RS files saved
        if res.rs_files:
            print(f"💾 RS time series files saved:")
            for rs_type, filepath in res.rs_files.items():
                print(f"   - {rs_type}: {filepath.name}")
    else:
        print(f"⚠️  Enhanced RS scores not computed (no metadata provided)")

    # 4) Inner-join to ensure only tickers with valid features appear
    print(f"\n🔗 Joining metadata, profiles, and features...")

    # Remove duplicate columns from snapshot (Theme, Subtheme already in meta)
    snapshot_clean = snapshot.drop(columns=['Theme', 'Subtheme'], errors='ignore')

    dashboard = (
        meta
        .join(profiles, how="left")
        .join(snapshot_clean, how="inner")
    )
    print(f"✅ Dashboard created with {dashboard.shape[0]} tickers and {dashboard.shape[1]} columns")

    # 5) Write out
    if output_path is None:
        ts = res.snapshot_file.stem.split("_")[-1]
        today_processed_dir = get_processed_dir()
        output_path = today_processed_dir / f"dashboard_{ts}.parquet"
    else:
        output_path = Path(output_path)

    dashboard.to_parquet(output_path, engine="fastparquet")
    print(f"🚀 Dashboard written to: {output_path}")
    
    return dashboard, output_path

if __name__ == "__main__":
    # Default configuration
    print("🚀 Starting Stealth Agency Trading Pipeline...")
    print("=" * 60)
    
    # You can customize these parameters as needed
    config = {
        "input_csv": "default_tickers.csv",
        "lookback": 365,
        "close_horizons": ['return_1d', 'return_20d', 'return_22d', 'return_200d'],
        "rs_horizons": ['return_20d', 'return_22d', 'return_200d'],
        "weights": None,  # Will use default equal weights
        "sma_windows": [5, 10, 20, 120]
    }
    
    print(f"📋 Configuration:")
    print(f"   Input CSV: {config['input_csv']}")
    print(f"   Lookback days: {config['lookback']}")
    print(f"   Close horizons: {config['close_horizons']}")
    print(f"   RS horizons: {config['rs_horizons']}")
    print(f"   SMA windows: {config['sma_windows']}")
    print()
    
    try:
        # Run the main pipeline
        dashboard, output_path = main(
            input_csv=config["input_csv"],
            lookback=config["lookback"],
            close_horizons=config["close_horizons"],
            rs_horizons=config["rs_horizons"],
            weights=config["weights"],
            sma_windows=config["sma_windows"]
        )
        
        print("\n" + "=" * 60)
        print("🎉 Pipeline completed successfully!")
        print(f"📁 Output file: {output_path}")
        print(f"📊 Dashboard shape: {dashboard.shape}")
        print("📁 Check the data/processed/yyyy_mm_dd/ directory for all output files")
        print("📋 Check the logs/ directory for detailed logs")
        
        # Show a sample of the results
        print(f"\n📈 Top 5 performers by different RS types:")

        # Global RS leaders
        if 'rs_global' in dashboard.columns:
            print(f"\n🌍 Top 5 Global RS Leaders:")
            top_global = dashboard.nlargest(5, 'rs_global')[['companyName', 'Theme', 'rs_global', 'rs_theme', 'rs_subtheme']]
            print(top_global.round(4).to_string())

            # Theme leaders
            print(f"\n🎯 Top performers by Theme:")
            for theme in dashboard['Theme'].unique()[:3]:  # Show first 3 themes
                theme_data = dashboard[dashboard['Theme'] == theme]
                if len(theme_data) > 0:
                    top_in_theme = theme_data.nlargest(2, 'rs_theme')[['companyName', 'rs_theme', 'rs_global']]
                    print(f"\n{theme}:")
                    print(top_in_theme.round(4).to_string())
        else:
            # Fallback to legacy RS score
            print(f"\n📊 Top 5 by legacy RS score:")
            top_legacy = dashboard.nlargest(5, 'rs_score')[['companyName', 'rs_score', 'close', 'return_20d']]
            print(top_legacy.to_string())
        
    except Exception as e:
        print(f"\n❌ Pipeline failed with error: {e}")
        print("📋 Check the logs for more details")
        raise
