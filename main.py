# main.py

import pandas as pd
from pathlib import Path
from downloader import DataDownloader
from feature_engineer import FeatureEngineer
from project_settings.config import get_processed_dir

def main(
    input_csv: str,
    output_path: str = None,
    lookback: int = 365,
    close_horizons=None,
    rs_horizons=None,
    weights=None,
    sma_windows=None
):
    # 1) Load your base CSV (must include columns: Theme, Subtheme, ticker)
    meta = pd.read_csv(input_csv).set_index("ticker")
    meta = meta[~meta.index.duplicated()]

    # 2) Download prices + profiles
    print(f"🔄 Initializing DataDownloader...")
    dl = DataDownloader(
        lookback=lookback,
        price_freshness_hours=24,
        profile_freshness_hours=24
    )

    print(f"📊 Processing {len(meta)} unique tickers...")
    close_df = dl.fetch_close_df(meta.index.tolist())
    profiles = dl.fetch_company_profiles(meta.index.tolist())

    # 3) Compute features
    fe = FeatureEngineer(
        close_horizons=close_horizons,
        rs_horizons=rs_horizons,
        weights=weights,
        sma_windows=sma_windows
    )
    res = fe.compute(close_df)
    snapshot = res.snapshot  # has 'data_date'

    # 4) Inner-join to ensure only tickers with valid features appear
    dashboard = (
        meta
        .join(profiles, how="left")
        .join(snapshot, how="inner")
    )

    # 5) Write out
    if output_path is None:
        ts = res.snapshot_file.stem.split("_")[-1]
        today_processed_dir = get_processed_dir()
        output_path = today_processed_dir / f"dashboard_{ts}.parquet"
    else:
        output_path = Path(output_path)

    dashboard.to_parquet(output_path, engine="fastparquet")
    print(f"🚀 Dashboard written to: {output_path}")

# ----------------------------------------------------------------
# Example invocation (no CLI required):
main("default_tickers.csv")
#df = pd.read_parquet("S:\QuantPyCharmProjects\QuantTrading\stealth_agency\data\processed\dashboard_201713.parquet")
#print(df.head())