#!/usr/bin/env python3
"""
Clear demonstration of the three RS time series per ticker:
1. Global RS Time Series - ticker vs ALL tickers
2. Theme RS Time Series - ticker vs SAME theme tickers  
3. Subtheme RS Time Series - ticker vs SAME subtheme tickers

Higher values = better performance in ALL cases.
"""

import pandas as pd
import numpy as np
from backend.downloader import DataDownloader
from backend.feature_engineer import FeatureEngineer
from relative_strength_calculator import RelativeStrengthCalculator

def demonstrate_rs_timeseries():
    """Demonstrate the three RS time series per ticker with clear examples."""
    print("🎯 RS TIME SERIES DEMONSTRATION")
    print("=" * 60)
    print("For EACH ticker, we compute THREE time series:")
    print("1. 🌍 Global RS: ticker vs ALL tickers (0-1, higher=better)")
    print("2. 🎯 Theme RS: ticker vs SAME theme tickers (0-1, higher=better)")  
    print("3. 🎪 Subtheme RS: ticker vs SAME subtheme tickers (0-1, higher=better)")
    print()
    
    # 1. Load sample data - focus on a few tickers for clarity
    print("📂 Loading sample data...")
    meta = pd.read_csv("default_tickers.csv").set_index("ticker")
    meta = meta[~meta.index.duplicated()]
    
    # Select specific tickers for clear demonstration
    demo_tickers = ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'AMD', 'INTC', 'TSLA', 'META']
    available_tickers = [t for t in demo_tickers if t in meta.index]
    meta_sample = meta.loc[available_tickers]
    
    print(f"✅ Demo tickers: {available_tickers}")
    print(f"📊 Themes represented: {meta_sample['Theme'].unique()}")
    print(f"📊 Subthemes represented: {meta_sample['Subtheme'].unique()}")
    print()
    
    # 2. Download price data
    print("📈 Downloading price data...")
    dl = DataDownloader(lookback=60, price_freshness_hours=24)  # Shorter for demo
    close_df = dl.fetch_close_df(available_tickers)
    print(f"✅ Downloaded {close_df.shape[0]} days of data for {close_df.shape[1]} tickers")
    print()
    
    # 3. Compute returns
    print("⚙️  Computing returns...")
    fe = FeatureEngineer()
    returns = fe._compute_returns(close_df)
    print(f"✅ Computed returns for horizons: {list(returns.keys())}")
    print()
    
    # 4. Initialize RS Calculator
    print("🎯 Initializing RS Calculator...")
    rs_calc = RelativeStrengthCalculator(
        rs_horizons=['return_20d'],  # Single horizon for clarity
        weights={'return_20d': 1.0}
    )
    print()
    
    # 5. Compute all RS scores
    print("🔄 Computing all RS time series...")
    rs_scores = rs_calc.compute_all_rs_scores(returns, meta_sample)
    print()
    
    # 6. Demonstrate the three time series for specific tickers
    print("📊 DEMONSTRATION: THREE TIME SERIES PER TICKER")
    print("=" * 60)
    
    for ticker in available_tickers[:3]:  # Show first 3 tickers
        if ticker not in rs_scores['global'].columns:
            continue
            
        print(f"\n🔍 TICKER: {ticker}")
        ticker_meta = meta_sample.loc[ticker]
        print(f"   Theme: {ticker_meta['Theme']}")
        print(f"   Subtheme: {ticker_meta['Subtheme']}")
        
        # Get the three time series for this ticker
        global_ts = rs_scores['global'][ticker].dropna()
        theme_ts = rs_scores['theme'][ticker].dropna()
        subtheme_ts = rs_scores['subtheme'][ticker].dropna()
        
        print(f"\n   📈 TIME SERIES DATA (Last 5 days):")
        print(f"   {'Date':<12} {'Global RS':<10} {'Theme RS':<10} {'Subtheme RS':<12}")
        print(f"   {'-'*12} {'-'*10} {'-'*10} {'-'*12}")
        
        # Show last 5 days
        for date in global_ts.tail(5).index:
            global_val = global_ts.loc[date] if date in global_ts.index else np.nan
            theme_val = theme_ts.loc[date] if date in theme_ts.index else np.nan
            subtheme_val = subtheme_ts.loc[date] if date in subtheme_ts.index else np.nan
            
            print(f"   {date.strftime('%Y-%m-%d'):<12} {global_val:<10.4f} {theme_val:<10.4f} {subtheme_val:<12.4f}")
        
        # Interpretation
        latest_date = global_ts.index[-1]
        latest_global = global_ts.iloc[-1]
        latest_theme = theme_ts.iloc[-1] if len(theme_ts) > 0 else np.nan
        latest_subtheme = subtheme_ts.iloc[-1] if len(subtheme_ts) > 0 else np.nan
        
        print(f"\n   🎯 INTERPRETATION (Latest: {latest_date.strftime('%Y-%m-%d')}):")
        print(f"   Global RS: {latest_global:.4f} = {latest_global*100:.1f}th percentile vs ALL tickers")
        if not np.isnan(latest_theme):
            print(f"   Theme RS:  {latest_theme:.4f} = {latest_theme*100:.1f}th percentile vs {ticker_meta['Theme']} tickers")
        if not np.isnan(latest_subtheme):
            print(f"   Subtheme:  {latest_subtheme:.4f} = {latest_subtheme*100:.1f}th percentile vs {ticker_meta['Subtheme']} tickers")
    
    # 7. Show theme comparison
    print(f"\n\n🎯 THEME COMPARISON EXAMPLE")
    print("=" * 60)
    
    # Find tickers in same theme
    ai_tickers = meta_sample[meta_sample['Theme'] == 'AI'].index.tolist()
    ai_tickers = [t for t in ai_tickers if t in rs_scores['global'].columns]
    
    if len(ai_tickers) >= 2:
        print(f"AI Theme tickers: {ai_tickers}")
        print(f"\nLatest Theme RS scores (within AI theme):")
        latest_date = rs_scores['theme'].index[-1]
        
        for ticker in ai_tickers:
            theme_rs = rs_scores['theme'].loc[latest_date, ticker]
            global_rs = rs_scores['global'].loc[latest_date, ticker]
            print(f"   {ticker}: Theme RS = {theme_rs:.4f}, Global RS = {global_rs:.4f}")
        
        print(f"\nNote: Theme RS scores sum to ~{len(ai_tickers)/2:.1f} (median rank in group)")
        print(f"      Higher Theme RS = better performance within AI theme")
    
    # 8. Validation checks
    print(f"\n\n✅ VALIDATION CHECKS")
    print("=" * 60)
    
    # Check that all values are between 0 and 1
    for rs_type, rs_data in rs_scores.items():
        if rs_type.endswith('_ranks'):
            continue
        min_val = rs_data.min().min()
        max_val = rs_data.max().max()
        print(f"{rs_type.capitalize()} RS range: [{min_val:.4f}, {max_val:.4f}] ✅" if 0 <= min_val <= max_val <= 1 else f"{rs_type} RS range: [{min_val:.4f}, {max_val:.4f}] ❌")
    
    # Check that higher returns lead to higher RS scores
    print(f"\nValidating that higher returns → higher RS scores:")
    latest_date = rs_scores['global'].index[-1]
    latest_returns = returns['return_20d'].loc[latest_date].dropna()
    latest_global_rs = rs_scores['global'].loc[latest_date]
    
    # Correlation should be positive and high
    correlation = latest_returns.corr(latest_global_rs)
    print(f"Correlation(returns, global_rs) = {correlation:.4f} ✅" if correlation > 0.8 else f"Correlation(returns, global_rs) = {correlation:.4f} ⚠️")
    
    return rs_scores, meta_sample

def analyze_ticker_evolution(rs_scores: dict, ticker: str, meta: pd.DataFrame):
    """Analyze how a ticker's RS scores evolve over time."""
    print(f"\n📈 TICKER EVOLUTION ANALYSIS: {ticker}")
    print("=" * 50)
    
    if ticker not in rs_scores['global'].columns:
        print(f"❌ {ticker} not found in RS data")
        return
    
    ticker_info = meta.loc[ticker]
    print(f"Theme: {ticker_info['Theme']}")
    print(f"Subtheme: {ticker_info['Subtheme']}")
    
    # Get time series
    global_ts = rs_scores['global'][ticker].dropna()
    theme_ts = rs_scores['theme'][ticker].dropna()
    subtheme_ts = rs_scores['subtheme'][ticker].dropna()
    
    print(f"\n📊 RS Score Evolution:")
    print(f"   Time period: {global_ts.index[0].strftime('%Y-%m-%d')} to {global_ts.index[-1].strftime('%Y-%m-%d')}")
    print(f"   Data points: {len(global_ts)}")
    
    # Calculate trends
    global_trend = global_ts.diff().mean()
    theme_trend = theme_ts.diff().mean() if len(theme_ts) > 1 else 0
    subtheme_trend = subtheme_ts.diff().mean() if len(subtheme_ts) > 1 else 0
    
    print(f"\n📈 Trends (daily average change):")
    print(f"   Global RS trend: {global_trend:+.6f} {'📈' if global_trend > 0 else '📉' if global_trend < 0 else '➡️'}")
    print(f"   Theme RS trend:  {theme_trend:+.6f} {'📈' if theme_trend > 0 else '📉' if theme_trend < 0 else '➡️'}")
    print(f"   Subtheme trend:  {subtheme_trend:+.6f} {'📈' if subtheme_trend > 0 else '📉' if subtheme_trend < 0 else '➡️'}")
    
    # Volatility
    print(f"\n📊 Volatility (standard deviation):")
    print(f"   Global RS volatility: {global_ts.std():.4f}")
    print(f"   Theme RS volatility:  {theme_ts.std():.4f}")
    print(f"   Subtheme volatility:  {subtheme_ts.std():.4f}")

if __name__ == "__main__":
    try:
        # Run the demonstration
        rs_scores, meta_sample = demonstrate_rs_timeseries()
        
        # Analyze specific ticker evolution
        if len(meta_sample) > 0:
            sample_ticker = meta_sample.index[0]
            analyze_ticker_evolution(rs_scores, sample_ticker, meta_sample)
        
        print(f"\n🎉 RS Time Series Demo completed successfully!")
        print(f"📋 Key takeaways:")
        print(f"   • Each ticker has 3 separate RS time series")
        print(f"   • All RS scores: 0-1 scale where 1=best, 0=worst")
        print(f"   • Global RS: vs all tickers")
        print(f"   • Theme RS: vs same theme tickers")
        print(f"   • Subtheme RS: vs same subtheme tickers")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        raise
